<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="opportunityId" column="opportunity_id" jdbcType="INTEGER" />
        <result property="insureNum" column="insure_num" jdbcType="INTEGER" />
        <result property="hasHistoryPolicy" column="has_history_policy" jdbcType="CHAR" />
        <result property="policyExpireTime" column="policy_expire_time" jdbcType="DATE" />
        <result property="isBid" column="is_bid" jdbcType="INTEGER" />
        <result property="bidResult" column="bid_result" jdbcType="INTEGER" />
        <result property="bidStartDate" column="bid_start_date" jdbcType="TIMESTAMP" />
        <result property="bidEndDate" column="bid_end_date" jdbcType="TIMESTAMP" />
        <result property="premiumBudget" column="premium_budget" jdbcType="INTEGER" />
        <result property="contacter" column="contacter" jdbcType="VARCHAR" />
        <result property="contacterPost" column="contacter_post" jdbcType="VARCHAR" />
        <result property="attchService" column="attch_service" jdbcType="INTEGER" />
        <result property="addHealthService" column="add_health_service" jdbcType="CHAR" />
        <result property="addRescueService" column="add_rescue_service" jdbcType="CHAR" />
        <result property="healthServiceCode" column="health_service_code" jdbcType="VARCHAR" />
        <result property="healthServiceName" column="health_service_name" jdbcType="VARCHAR" />
        <result property="rescueServiceCode" column="rescue_service_code" jdbcType="VARCHAR" />
        <result property="rescueServiceName" column="rescue_service_name" jdbcType="VARCHAR" />
        <result property="employeeInsuranceType" column="employee_insurance_type" jdbcType="VARCHAR" />
        <result property="generalInsuranceType" column="general_insurance_type" jdbcType="VARCHAR" />
        <result property="remark" column="remark" jdbcType="VARCHAR" />
        <result property="teamTime" column="team_time" jdbcType="TIMESTAMP" />
        <result property="submitTime" column="submit_time" jdbcType="TIMESTAMP" />
        <result property="logTime" column="log_time" jdbcType="TIMESTAMP" />
        <result property="kycReportTime" column="kyc_report_time" jdbcType="TIMESTAMP" />
        <result property="riskReportTime" column="risk_report_time" jdbcType="TIMESTAMP" />
        <result property="coordinationAcceptTime" column="coordination_accept_time" jdbcType="TIMESTAMP" />
        <result property="assignCoordinationTime" column="assign_coordination_time" jdbcType="VARCHAR" />
        <result property="assignProjectManagerTime" column="assign_project_manager_time" jdbcType="VARCHAR" />
        <result property="projectTeamInfo" column="project_team_info" jdbcType="LONGVARCHAR" />
        <result property="closeTime" column="close_time" jdbcType="VARCHAR" />
        <result property="summaryTime" column="summary_time" jdbcType="TIMESTAMP" />
        <result property="rankingTime" column="ranking_time" jdbcType="VARCHAR" />
        <result property="policyTime" column="policy_time" jdbcType="VARCHAR" />
        <result property="suspendTime" column="suspend_time" jdbcType="VARCHAR" />
        <result property="restartTime" column="restart_time" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        id, opportunity_id, insure_num, has_history_policy, policy_expire_time, is_bid, bid_result, 
        bid_start_date, bid_end_date, premium_budget,         contacter, contacter_post, attch_service, add_health_service, 
        add_rescue_service, health_service_code, health_service_name, rescue_service_code, rescue_service_name, 
        employee_insurance_type, general_insurance_type, remark, team_time, submit_time, log_time,
        kyc_report_time, risk_report_time, coordination_accept_time, assign_coordination_time, 
        assign_project_manager_time, project_team_info, close_time, summary_time, ranking_time, 
        policy_time, suspend_time, restart_time, create_id, create_time, update_id, update_time, 
        is_deleted, tenant_id
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.opportunity_id,
        t.insure_num,
        t.has_history_policy,
        t.policy_expire_time,
        t.is_bid,
        t.bid_result,
        t.bid_start_date,
        t.bid_end_date,
        t.premium_budget,
        t.contacter,
        t.contacter_post,
        t.attch_service,
        t.add_health_service,
        t.add_rescue_service,
        t.health_service_code,
        t.health_service_name,
        t.rescue_service_code,
        t.rescue_service_name,
        t.employee_insurance_type,
        t.general_insurance_type,
        t.remark,
        t.team_time,
        t.submit_time,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.tenant_id,
        t.is_deleted,
        t.log_time
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="opportunityId != null">
                and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
            </if>
            <if test="insureNum != null">
                and t.insure_num = #{insureNum,jdbcType=INTEGER}
            </if>
            <if test="hasHistoryPolicy != null">
                and t.has_history_policy = #{hasHistoryPolicy,jdbcType=CHAR}
            </if>
            <if test="policyExpireTime != null">
                and t.policy_expire_time = #{policyExpireTime,jdbcType=DATE}
            </if>
            <if test="bidResult != null">
                and t.bid_result = #{bidResult}
            </if>
            <if test="isBid != null">
                and t.is_bid = #{isBid,jdbcType=INTEGER}
            </if>
            <if test="bidStartDate != null">
                and t.bid_start_date = #{bidStartDate,jdbcType=DATE}
            </if>
            <if test="bidEndDate != null">
                and t.bid_end_date = #{bidEndDate,jdbcType=DATE}
            </if>
            <if test="premiumBudget != null">
                and t.premium_budget = #{premiumBudget,jdbcType=INTEGER}
            </if>
            <if test="contacter != null">
                and t.contacter = #{contacter,jdbcType=VARCHAR}
            </if>
            <if test="contacterPost != null">
                and t.contacter_post = #{contacterPost,jdbcType=VARCHAR}
            </if>
            <if test="attchService != null">
                and t.attch_service = #{attchService,jdbcType=INTEGER}
            </if>
            <if test="addHealthService != null">
                and t.add_health_service = #{addHealthService,jdbcType=CHAR}
            </if>
            <if test="addRescueService != null">
                and t.add_rescue_service = #{addRescueService,jdbcType=CHAR}
            </if>
            <if test="healthServiceCode != null">
                and t.health_service_code = #{healthServiceCode,jdbcType=VARCHAR}
            </if>
            <if test="healthServiceName != null">
                and t.health_service_name = #{healthServiceName,jdbcType=VARCHAR}
            </if>
            <if test="rescueServiceCode != null">
                and t.rescue_service_code = #{rescueServiceCode,jdbcType=VARCHAR}
            </if>
            <if test="rescueServiceName != null">
                and t.rescue_service_name = #{rescueServiceName,jdbcType=VARCHAR}
            </if>
            <if test="employeeInsuranceType != null">
                and t.employee_insurance_type = #{employeeInsuranceType,jdbcType=VARCHAR}
            </if>
            <if test="generalInsuranceType != null">
                and t.general_insurance_type = #{generalInsuranceType,jdbcType=VARCHAR}
            </if>
            <if test="remark != null">
                and t.remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="teamTime != null">
                and t.team_time = #{teamTime,jdbcType=TIMESTAMP}
            </if>
            <if test="submitTime != null">
                and t.submit_time = #{submitTime,jdbcType=TIMESTAMP}
            </if>
            <if test="restartTime != null">
                and t.restart_time = #{restartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="logTime != null">
                and t.log_time = #{logTime,jdbcType=TIMESTAMP}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_opportunity_detail where id = #{id} and is_deleted = 0
    </select>

    <select id="selectByOpportunityId" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from t_opportunity_detail 
        where opportunity_id = #{opportunityId} 
        and tenant_id = #{tenantId}
        and is_deleted = 0
        order by update_time desc
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_opportunity_detail t
        <include refid="Base_Condition"/>
        order by t.update_time desc
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_opportunity_detail t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_detail (
            opportunity_id, insure_num, has_history_policy, policy_expire_time, is_bid, bid_result, bid_start_date, bid_end_date, premium_budget, contacter, contacter_post, attch_service, add_health_service, add_rescue_service, health_service_code, health_service_name, rescue_service_code, rescue_service_name, employee_insurance_type, general_insurance_type, remark, team_time, submit_time, log_time, kyc_report_time, risk_report_time, coordination_accept_time, assign_coordination_time, assign_project_manager_time, project_team_info, close_time, summary_time, ranking_time, policy_time, suspend_time, restart_time, create_id, create_time, update_id, update_time, tenant_id, is_deleted
        ) values (
            #{opportunityId}, #{insureNum}, #{hasHistoryPolicy}, #{policyExpireTime}, #{isBid}, #{bidResult}, #{bidStartDate}, #{bidEndDate}, #{premiumBudget}, #{contacter}, #{contacterPost}, #{attchService}, #{addHealthService}, #{addRescueService}, #{healthServiceCode}, #{healthServiceName}, #{rescueServiceCode}, #{rescueServiceName}, #{employeeInsuranceType}, #{generalInsuranceType}, #{remark}, #{teamTime}, #{submitTime}, #{logTime}, #{kycReportTime}, #{riskReportTime}, #{coordinationAcceptTime}, #{assignCoordinationTime}, #{assignProjectManagerTime}, #{projectTeamInfo}, #{closeTime}, #{summaryTime}, #{rankingTime}, #{policyTime}, #{suspendTime}, #{restartTime}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, #{tenantId}, 0
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="insureNum != null">insure_num,</if>
            <if test="hasHistoryPolicy != null">has_history_policy,</if>
            <if test="policyExpireTime != null">policy_expire_time,</if>
            <if test="isBid != null">is_bid,</if>
            <if test="bidResult != null">bid_result,</if>
            <if test="bidStartDate != null">bid_start_date,</if>
            <if test="bidEndDate != null">bid_end_date,</if>
            <if test="premiumBudget != null">premium_budget,</if>
            <if test="contacter != null">contacter,</if>
            <if test="contacterPost != null">contacter_post,</if>
            <if test="attchService != null">attch_service,</if>
            <if test="addHealthService != null">add_health_service,</if>
            <if test="addRescueService != null">add_rescue_service,</if>
            <if test="healthServiceCode != null">health_service_code,</if>
            <if test="healthServiceName != null">health_service_name,</if>
            <if test="rescueServiceCode != null">rescue_service_code,</if>
            <if test="rescueServiceName != null">rescue_service_name,</if>
            <if test="employeeInsuranceType != null">employee_insurance_type,</if>
            <if test="generalInsuranceType != null">general_insurance_type,</if>
            <if test="remark != null">remark,</if>
            <if test="teamTime != null">team_time,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="restartTime != null">restart_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="insureNum != null">#{insureNum},</if>
            <if test="hasHistoryPolicy != null">#{hasHistoryPolicy},</if>
            <if test="policyExpireTime != null">#{policyExpireTime},</if>
            <if test="isBid != null">#{isBid},</if>
            <if test="bidResult != null">#{bidResult},</if>
            <if test="bidStartDate != null">#{bidStartDate},</if>
            <if test="bidEndDate != null">#{bidEndDate},</if>
            <if test="premiumBudget != null">#{premiumBudget},</if>
            <if test="contacter != null">#{contacter},</if>
            <if test="contacterPost != null">#{contacterPost},</if>
            <if test="attchService != null">#{attchService},</if>
            <if test="addHealthService != null">#{addHealthService},</if>
            <if test="addRescueService != null">#{addRescueService},</if>
            <if test="healthServiceCode != null">#{healthServiceCode},</if>
            <if test="healthServiceName != null">#{healthServiceName},</if>
            <if test="rescueServiceCode != null">#{rescueServiceCode},</if>
            <if test="rescueServiceName != null">#{rescueServiceName},</if>
            <if test="employeeInsuranceType != null">#{employeeInsuranceType},</if>
            <if test="generalInsuranceType != null">#{generalInsuranceType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="teamTime != null">#{teamTime},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="restartTime != null">#{restartTime},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail">
        update t_opportunity_detail set
            opportunity_id = #{opportunityId},
            insure_num = #{insureNum},
            has_history_policy = #{hasHistoryPolicy},
            policy_expire_time = #{policyExpireTime},
            is_bid = #{isBid},
            bid_result = #{bidResult},
            bid_start_date = #{bidStartDate},
            bid_end_date = #{bidEndDate},
            premium_budget = #{premiumBudget},
            contacter = #{contacter},
            contacter_post = #{contacterPost},
            add_health_service = #{addHealthService},
            add_rescue_service = #{addRescueService},
            health_service_code = #{healthServiceCode},
            health_service_name = #{healthServiceName},
            rescue_service_code = #{rescueServiceCode},
            rescue_service_name = #{rescueServiceName},
            employee_insurance_type = #{employeeInsuranceType},
            general_insurance_type = #{generalInsuranceType},
            remark = #{remark},
            team_time = #{teamTime},
            submit_time = #{submitTime},
            restart_time = #{restartTime},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            tenant_id = #{tenantId},
            is_deleted = #{isDeleted},
            log_time = #{logTime},
            attch_service = #{attchService},
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail">
        update t_opportunity_detail
        <set>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="insureNum != null">insure_num = #{insureNum},</if>
            <if test="hasHistoryPolicy != null">has_history_policy = #{hasHistoryPolicy},</if>
            <if test="policyExpireTime != null">policy_expire_time = #{policyExpireTime},</if>
            <if test="isBid != null">is_bid = #{isBid},</if>
            <if test="bidResult != null">bid_result = #{bidResult},</if>
            <if test="bidStartDate != null">bid_start_date = #{bidStartDate},</if>
            <if test="bidEndDate != null">bid_end_date = #{bidEndDate},</if>
            <if test="premiumBudget != null">premium_budget = #{premiumBudget},</if>
            <if test="contacter != null">contacter = #{contacter},</if>
            <if test="contacterPost != null">contacter_post = #{contacterPost},</if>
            <if test="addHealthService != null">add_health_service = #{addHealthService},</if>
            <if test="addRescueService != null">add_rescue_service = #{addRescueService},</if>
            <if test="healthServiceCode != null">health_service_code = #{healthServiceCode},</if>
            <if test="healthServiceName != null">health_service_name = #{healthServiceName},</if>
            <if test="rescueServiceCode != null">rescue_service_code = #{rescueServiceCode},</if>
            <if test="rescueServiceName != null">rescue_service_name = #{rescueServiceName},</if>
            <if test="employeeInsuranceType != null">employee_insurance_type = #{employeeInsuranceType},</if>
            <if test="generalInsuranceType != null">general_insurance_type = #{generalInsuranceType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="teamTime != null">team_time = #{teamTime},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="restartTime != null">restart_time = #{restartTime},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="logTime != null">log_time = #{logTime},</if>
            <if test="attchService != null">attch_service = #{attchService},</if>

        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_opportunity_detail set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_opportunity_detail (
            opportunity_id, insure_num, has_history_policy, policy_expire_time, is_bid, bid_result, bid_start_date, bid_end_date, premium_budget, contacter, contacter_post, add_health_service, add_rescue_service, health_service_code, health_service_name, rescue_service_code, rescue_service_name, employee_insurance_type, general_insurance_type, remark, team_time, submit_time, create_id, create_time, update_id, update_time, tenant_id, is_deleted, log_time, kyc_report_time, risk_report_time, coordination_accept_time, assign_coordination_time, assign_project_manager_time, project_team_info, close_time, summary_time, ranking_time, policy_time, suspend_time, restart_time,attch_service
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.opportunityId}, #{item.insureNum}, #{item.hasHistoryPolicy}, #{item.policyExpireTime}, #{item.isBid}, #{item.bidResult}, #{item.bidStartDate}, #{item.bidEndDate}, #{item.premiumBudget}, #{item.contacter}, #{item.contacterPost}, #{item.addHealthService}, #{item.addRescueService}, #{item.healthServiceCode}, #{item.healthServiceName}, #{item.rescueServiceCode}, #{item.rescueServiceName}, #{item.employeeInsuranceType}, #{item.generalInsuranceType}, #{item.remark}, #{item.teamTime}, #{item.submitTime}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, #{item.tenantId}, 0, #{item.logTime}, #{item.kycReportTime}, #{item.riskReportTime}, #{item.coordinationAcceptTime}, #{item.assignCoordinationTime}, #{item.assignProjectManagerTime}, #{item.projectTeamInfo}, #{item.closeTime}, #{item.summaryTime}, #{item.rankingTime}, #{item.policyTime}, #{item.suspendTime}, #{item.restartTime}, #{item.attchService}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_opportunity_detail set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 新增：通过opportunity_id更新log_time（允许为空） -->
    <update id="updateLogTimeByOpportunityId" parameterType="java.util.Map">
        update t_opportunity_detail 
        set log_time = #{logTime}
        where opportunity_id = #{opportunityId}
        and is_deleted = 0
    </update>

    <update id="updateSummaryTimeByOpportunityId" parameterType="java.util.HashMap">
        update t_opportunity_detail
        set
        summary_time = #{summaryTime,jdbcType=TIMESTAMP} <!-- 允许为null，直接赋值 -->
        where
        opportunity_id = #{opportunityId,jdbcType=INTEGER}  <!-- 按机会ID匹配 -->
    </update>

    <!-- 新增：通过opportunity_id更新team_time（允许为空） -->
    <update id="updateTeamTimeByOpportunityId" parameterType="java.util.Map">
        update t_opportunity_detail 
        set team_time = #{teamTime}
        where opportunity_id = #{opportunityId}
        and is_deleted = 0
    </update>

    <!-- 新增：通过opportunity_id更新policy_time（允许为空） -->
    <update id="updatePolicyTimeByOpportunityId" parameterType="java.util.Map">
        update t_opportunity_detail 
        set policy_time = #{policyTime}
        where opportunity_id = #{opportunityId}
        and is_deleted = 0
    </update>
</mapper>