package com.kbao.kbcelms.formConfig.model;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.annotation.Excel;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 表单配置字段实体
 * @Date 2025-08-15
 */
@Data
@Document(collection = "FormConfigField")
public class FormConfigField implements Serializable {
    
    /**
     * MongoDB主键
     */
    @Id
    private String id;
    
    /**
     * 配置ID
     */
    @Indexed
    private Integer configId;
    
    /**
     * 字段名称
     */
    @Excel(name = "字段名称")
    private String fieldName;

    /**
     * 字段编码
     */
    @Excel(name = "字段编码")
    private String fieldCode;

    /**
     * 展示类型：1-输入框，2-单选框，3-下拉框，4-日期选择，5-日期范围，6-文本域
     */
    @Excel(name = "展示类型")
    private String showType;

    /**
     * 是否必填：0-否，1-是
     */
    @Excel(name = "是否必填")
    private String required;

    /**
     * 是否可修改：0-否，1-是
     */
    @Excel(name = "是否可修改")
    private String change;
    
    /**
     * 默认值
     */
    @Excel(name = "默认值")
    private String defaultValue;

    /**
     * 校验规则
     */
    @Excel(name = "校验规则")
    private String validation;

    /**
     * 附加属性（JSON格式）
     */
    private JSONObject additional;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;
    
    /**
     * 排序
     */
    @Excel(name = "序号")
    private Integer sort;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 附加属性字符串（用于前端展示和Excel导入导出）
     */
    @Transient
    @Excel(name = "附加属性")
    private String additionalStr;
}
