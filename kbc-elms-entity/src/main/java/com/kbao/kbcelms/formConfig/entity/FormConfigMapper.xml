<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.formConfig.dao.FormConfigMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.formConfig.entity.FormConfig">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="config_name" jdbcType="VARCHAR" property="configName" />
        <result column="config_code" jdbcType="VARCHAR" property="configCode" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_id" jdbcType="VARCHAR" property="createId" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_id" jdbcType="VARCHAR" property="updateId" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    </resultMap>

    <sql id="Base_Column_List">
        id, config_name, config_code, type, status, remark, create_time, create_id,
        update_time, update_id, is_deleted, tenant_id
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.config_name,
        t.config_code,
        t.type,
        t.status,
        t.remark,
        t.create_time,
        t.create_id,
        t.update_time,
        t.update_id,
        t.is_deleted,
        t.tenant_id
    </sql>

    <sql id="Base_Condition">
        <where>
        is_deleted = 0
        <if test="configName != null and configName != ''">
            and t.config_name like CONCAT('%', #{configName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="configCode != null and configCode != ''">
            and t.config_code like CONCAT('%', #{configCode,jdbcType=VARCHAR}, '%')
        </if>
        <if test="type != null and type != ''">
            and t.type = #{type,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and t.status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            and t.remark like CONCAT('%', #{remark,jdbcType=VARCHAR}, '%')
        </if>
        <if test="createTime != null">
            and t.create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createId != null and createId != ''">
            and t.create_id = #{createId,jdbcType=VARCHAR}
        </if>
        <if test="updateTime != null">
            and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateId != null and updateId != ''">
            and t.update_id = #{updateId,jdbcType=VARCHAR}
        </if>
        <if test="tenantId != null and tenantId != ''">
            and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
        </if>
        <!-- 自定义条件-->
        </where>
    </sql>

    <!-- 根据条件查询-->
    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List" />
        from t_form_config t
        <include refid="Base_Condition" />
        order by t.update_time desc
    </select>

    <!-- 根据条件统计数量-->
    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_form_config t
        <include refid="Base_Condition" />
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_form_config
        where id = #{id,jdbcType=INTEGER}
        and is_deleted = 0
    </select>

    <select id="selectByConfigCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from t_form_config
        where config_code = #{configCode,jdbcType=VARCHAR}
        and is_deleted = 0
    </select>

    <select id="selectByType" parameterType="java.lang.String" resultMap="BaseResultMap">
        select id, config_name
        from t_form_config
        where type = #{type} and status = '1' and is_deleted = 0
        order by create_time desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_form_config set is_deleted = 1
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.kbao.kbcelms.formConfig.entity.FormConfig" useGeneratedKeys="true" keyProperty="id">
        insert into t_form_config (config_name, config_code, type, status,
        remark, create_time, create_id, update_time, update_id, is_deleted, tenant_id)
        values (#{configName,jdbcType=VARCHAR}, #{configCode,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createId,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER},
        #{tenantId,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.formConfig.entity.FormConfig" useGeneratedKeys="true" keyProperty="id">
        insert into t_form_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null">config_name,</if>
            <if test="configCode != null">config_code,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="tenantId != null">tenant_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null">#{configName,jdbcType=VARCHAR},</if>
            <if test="configCode != null">#{configCode,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="createId != null">#{createId,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateId != null">#{updateId,jdbcType=VARCHAR},</if>
            <if test="isDeleted != null">#{isDeleted,jdbcType=INTEGER},</if>
            <if test="tenantId != null">#{tenantId,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.formConfig.entity.FormConfig">
        update t_form_config
        <set>
            <if test="configName != null">config_name = #{configName,jdbcType=VARCHAR},</if>
            <if test="configCode != null">config_code = #{configCode,jdbcType=VARCHAR},</if>
            <if test="type != null">type = #{type,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=VARCHAR},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateId != null">update_id = #{updateId,jdbcType=VARCHAR},</if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.formConfig.entity.FormConfig">
        update t_form_config
        set config_name = #{configName,jdbcType=VARCHAR},
            config_code = #{configCode,jdbcType=VARCHAR},
            type = #{type,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_id = #{updateId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 检查配置编码是否存在 -->
    <select id="isExistConfigCode" resultType="int">
        select count(1) from t_form_config 
        where config_code = #{configCode} 
        and is_deleted = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

</mapper>
