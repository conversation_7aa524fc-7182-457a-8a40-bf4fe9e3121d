package com.kbao.kbcelms.riskMatrix.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 核心评分项实体类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScoreItem {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 评分项名称
     */
    private String name;
    
    /**
     * 评分项编码
     */
    private String code;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 所属类别
     */
    private String category;
    
    /**
     * 权重
     */
    private BigDecimal weight;
    
    /**
     * 最大分值
     */
    private Integer maxScore;

    /**
     * 是否关联公式：0-否，1-是
     */
    private Integer isFormula;
    
    /**
     * 关联公式ID
     */
    private Long formulaId;
    
    /**
     * 公式名称
     */
    private String formulaName;
    
    /**
     * 系数
     */
    private BigDecimal coefficient;
    
    /**
     * 适用企业类型，逗号分隔
     */
    private String enterpriseTypes;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;
}
