<template>
  <div class="member-manage">
    <TableToolTemp :toolListProps="addMemberMenuListPros" class="log-tool" @handleTool="addMemberHandleTool"></TableToolTemp>
    <!-- 项目成员表格 -->
    <el-table :data="memberData" class="dt-table" style="width: 100%" v-hover row-key="id">
      <el-table-column align="center" prop="nickname" label="成员姓名"  width="120">
        <template slot-scope="scope">
          <span class="communication-item">{{ scope.row.nickname }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="userName" label="系统账号" width="120" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.userName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="organNamePath" label="人员归属" >
        <template slot-scope="scope">
          <span>{{ scope.row.organNamePath }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="roleName" label="角色名称" width="120">
        <template slot-scope="scope">
          <div >
            {{ scope.row.roleName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center"  label="参与状态" >
        <template slot-scope="scope">
          <div >
            {{ scope.row | getJoinType  }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" >
        <template slot-scope="scope">
          <div class="action-buttons" v-if="!(scope.row.isDefault==1 && scope.row.joinType==1) && !editDisable && isManager">
            <el-button class="btn-center" type="text" @click="inviteConfirm(scope.row)">
              确认邀请
            </el-button>
            <el-button class="btn-center" type="text" @click="removeMember(scope.row.id)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>


    <!-- 项目分工 -->
    <TableToolTemp :toolListProps="divisionOneMenuListPros" class="log-tool" @handleTool="addOneDivisionHandleTool">
      <template slot="right">
        <div style="text-align: left;width: 100%;padding: 10px 600px 0px 0px;color: red">
          * 确认比例后系统会推送结果至参项人员，并发送确认通知
        </div>
      </template>
    </TableToolTemp>
    <!-- 首次项目分工表格 -->
    <el-table :data="divisionOneData" class="dt-table" style="width: 100%" v-hover row-key="id">
      <el-table-column align="center" prop="name" label="项目分工"  width="120">
        <template slot-scope="scope">
          <span class="communication-item">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="nickName" label="负责人员" width="120" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.nickName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="divisionRatio" label="分配比例" >
        <template slot-scope="scope">
          <span>{{ scope.row.divisionRatio }}%</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ratio" label="分配比例上限" width="120">
        <template slot-scope="scope">
          <div >
            {{ scope.row.ratio }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="status" label="确认状态" >
        <template slot-scope="scope">
          <div >
            {{ scope.row |  getDivisionStatus }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="remark" label="说明" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.remark  }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" >
        <template slot-scope="scope">
          <div class="action-buttons" >
            <el-button class="btn-center" type="text" @click="openViewDivisionPopup(scope.row)">
              查看
            </el-button>
            <el-button class="btn-center" type="text" @click="openEditDivisionPopup(scope.row,1)" v-if="!editDisable && !hasSecond && isManager">
              编辑
            </el-button>
            <el-button class="btn-center" type="text" @click="divisionAcceptOrReject(scope.row.id,1)" v-if="!editDisable && !hasSecond && !isManager && scope.row.status==0">
              确认比例
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <TableToolTemp :toolListProps="divisionTwoMenuListPros" class="log-tool" @handleTool="addTwoDivisionHandleTool"></TableToolTemp>
    <!-- 二次项目分工表格 -->
    <el-table :data="divisionTwoData" class="dt-table" style="width: 100%" v-hover row-key="id">
      <el-table-column align="center" prop="name" label="项目分工"  width="120">
        <template slot-scope="scope">
          <span class="communication-item">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="nickName" label="负责人员" width="120" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.nickName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="divisionRatio" label="分配比例" >
        <template slot-scope="scope">
          <span>{{ scope.row.divisionRatio }}%</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ratio" label="分配比例上限" width="120">
        <template slot-scope="scope">
          <div >
            {{ scope.row.ratio }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="status" label="确认状态" >
        <template slot-scope="scope">
          <div >
            {{ scope.row |  getDivisionStatus }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="remark" label="说明" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.remark  }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" >
        <template slot-scope="scope">
          <div class="action-buttons" >
            <el-button class="btn-center" type="text" @click="openViewDivisionPopup(scope.row)">
              查看
            </el-button>
            <el-button class="btn-center" type="text" @click="openEditDivisionPopup(scope.row,2)" v-if="!editDisable && hasSecond && isManager">
              编辑
            </el-button>
            <el-button class="btn-center" type="text" @click="divisionAcceptOrReject(scope.row.id,2)" v-if="!editDisable && hasSecond && !isManager && scope.row.status==0">
              确认比例
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 填写保单 -->
    <TableToolTemp :toolListProps="insureMenuListPros" class="log-tool" @handleTool="insureHandleTool" v-if="isManager"></TableToolTemp>
    <!-- 二次项目分工表格 -->
    <el-table :data="insureList" class="dt-table" style="width: 100%" v-hover row-key="id" v-if="isManager">
      <el-table-column align="center" prop="companyCode" label="保险公司名称"  width="250">
        <template slot-scope="scope">
          <span class="communication-item">{{ scope.row.companyName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="policyNo" label="保单号" width="250" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.policyNo }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" >
        <template slot-scope="scope">
          <div class="action-buttons" v-if="!editDisable">
            <el-button class="btn-center" type="text" @click="opendEditInsureOrder(scope.row,2)">
              编辑
            </el-button>
            <el-button class="btn-center" type="text" @click="confirmRemoveOrder(scope.row)">
              删除
            </el-button>

          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 邀请确认弹窗 -->
    <DtPopup :isShow.sync="showInivitePopup" @close="showInivitePopup=false" @confirm="inviteConfirmSend" title="发送确认邀请" small :footer="true" width="800px">
      <div class="text-content">
        <div >是否确认发送邀请通知给 {{inviteMember.roleName}} {{inviteMember.nickname}}（{{inviteMember.userName}}）进行组队确认？</div>
        <div v-if="inviteMember.times>0">已发送次数：{{inviteMember.times}}</div>
        <div>通知方式：邮件通知、系统提醒</div>
        <div>邮件地址：{{inviteMember.email}}</div>
      </div>
    </DtPopup>

    <!-- 新增项目成员弹窗 -->
    <DtPopup :isShow.sync="showAddMemberPopup" @close="closeAddMemberPopup" @confirm="addMemberConfirm" title="新增项目成员" center :footer="true" width="1000px">
      <div class="delete-confirm">
        <SearchForm
          :searchForm="addMemberParam"
          :searchFormTemp="addMemberFormTemp"
          @normalSearch="addMemberNormalSearch"
          @normalResetQuery="addMemberNormalResetQuery"
        ></SearchForm>
        <el-table v-hover :data="branchUserData" stripe class="dt-table" style="width: 100%"
                  highlight-current-row
                  @current-change="addMemberSelect"
        >
          <el-table-column width="55" fixed="left">
            <template slot-scope="scope">
              <el-radio
                class="radio"
                :label="scope.row"
                v-model="addMemberSelectRow"
              >{{""}}</el-radio>
            </template>

          </el-table-column>
          <el-table-column align="center" prop="nickName" label="人员姓名" width="120"></el-table-column>
          <el-table-column align="center" prop="roleType" label="人员角色" width="120">
            <template slot-scope="scope">
              <div >
                {{ scope.row.roleType | getDicItemName("elms.role.type")  }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="organName" label="人员归属" width="200"></el-table-column>
          <el-table-column align="center" prop="identityCode" label="擅长险种"  width="200"></el-table-column>
          <el-table-column align="center" prop="identityCode" label="擅长行业"  width="200"></el-table-column>
        </el-table>
        <!--      <Pagination-->
        <!--        :pageData="addMemberParam"-->
        <!--        :total="userTotal"-->
        <!--        layout="total, sizes, prev, pager, next, jumper"-->
        <!--        @size-change="addMemberHandleSizeChange"-->
        <!--        @current-change="addMemberHandleCurrentChange"-->
        <!--      ></Pagination>-->
      </div>

    </DtPopup>

    <!-- 新增项目分工窗 -->
    <DtPopup :isShow.sync="showAddDivisionPopup" @close="showAddDivisionPopup=false" @confirm="addDivision" :title="divisionPopupTitle" small :footer="true" width="800" >
      <el-form
        ref="addDivisionForm"
        :model="addDivisionForm"
        :rules="divisionFormRules"
        label-width="110px"
        label-position="left"
      >
        <el-form-item label="项目分工" prop="divisionId">
          <el-select v-model="addDivisionForm.divisionId" filterable class="dt-input-width" placeholder="请选择" @change="handleDivisionChange">
            <el-option v-for="(item, index) in divisionRatioList" :label="item.name" :value="item.id"
                       :key="index">
            </el-option>

          </el-select>
        </el-form-item>
        <el-form-item label="负责人员" prop="userId">
          <el-select v-model="addDivisionForm.userId" filterable class="dt-input-width" placeholder="请选择" :disabled="divisionMode=='edit'">
            <el-option v-for="(item, index) in memberData" :label="item.nickname" :value="item.userId"
                       :key="index">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上限比例">
          {{selectDivisionRatio}}%
        </el-form-item>

        <el-form-item label="分配比例" prop="divisionRatio">
          <el-input v-model="addDivisionForm.divisionRatio" placeholder="请输入分配比例"  style="width: 90%;margin-right: 5px" />%
        </el-form-item>
        <el-form-item label="说明" prop="remark">
          <el-input
            v-model="addDivisionForm.remark"
            type="textarea"
            class="dt-input-width"
            :rows="4"
            :maxlength="200"
            show-word-limit
            placeholder="请输入分工说明"
          ></el-input>
        </el-form-item>
      </el-form>
    </DtPopup>

    <!-- 项目分工查看弹窗 -->
    <DtPopup :isShow.sync="showViewDivisionPopup" @close="showViewDivisionPopup=false"  title="查看项目分工" small :footer="false" width="800">
      <el-form
        ref="viewDivisionForm"
        :model="viewDivisionForm"
        label-width="110px"
        label-position="left"
      >
        <el-form-item label="项目分工" prop="divisionId">
          {{viewDivisionForm.name}}
        </el-form-item>
        <el-form-item label="负责人员" prop="userId">
          {{viewDivisionForm.nickName}}
        </el-form-item>
        <el-form-item label="上限比例">
          {{viewDivisionForm.ratio}}%
        </el-form-item>

        <el-form-item label="分配比例" prop="divisionRatio">
          {{viewDivisionForm.divisionRatio}}%
        </el-form-item>
        <el-form-item label="说明" prop="remark">
          <div>{{viewDivisionForm.remark}}</div>
        </el-form-item>
      </el-form>
    </DtPopup>

    <!-- 项目分工确认弹窗 -->
    <DtPopup :isShow.sync="showDivisionNoticePopup" @close="showDivisionNoticePopup=false" @confirm="sendDivisionNoticeConfirm"  :title="divisionNoticeTitle" :footer="true" width="800px">
      <!-- 首次项目分工表格 -->
      <el-table :data="divisionNoticeData" class="dt-table" style="width: 100%" v-hover row-key="id">
        <el-table-column align="center" prop="nickName" label="负责人员"  width="100">
          <template slot-scope="scope">
            <span class="communication-item">{{ scope.row.nickName}}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="name" :label="divisionColumnTitle.name" width="120" >
          <template slot-scope="scope">
            <div >
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="divisionRatio" :label="divisionColumnTitle.ratio" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.divisionRatio }}%</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="ratio" label="通知方式" >
          <template slot-scope="scope">
            <div >
              邮件、系统提示
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="邮件地址" >
          <template slot-scope="scope">
            <div >
              {{ scope.row.email  }}
            </div>
          </template>
        </el-table-column>

      </el-table>
    </DtPopup>

    <!-- 填写保单号弹窗 -->
    <DtPopup :isShow.sync="showInsurePopup" @close="showInsurePopup=false" @confirm="saveInsurePolicyNo"  title="填写保单号" :footer="true" width="800px">

      <div class="location-box" v-if="insureMode=='add'">
        <div class="left" @click="addInsureInputData()">
          <i class="el-icon-plus" :style="{color:themeObj.color}"></i>
          <span class="location" :style="{color:themeObj.color}">添加</span>
        </div>
      </div>
      <el-table :data="insureInputData" class="dt-table" style="width: 100%" v-hover row-key="id">
        <el-table-column align="center" prop="companyCode" label="保险公司"  width="250">
          <template slot-scope="scope">
            <el-select v-model="scope.row.companyCode" filterable  placeholder="请选择" style="width: 200px" >
              <el-option v-for="(item, index) in insureCompanyList" :label="item.comFullName" :value="item.comCode"
                         :key="index">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="policyNo" label="保单号"  >
          <template slot-scope="scope">
            <el-input v-model="scope.row.policyNo" placeholder="请输入保单号"   />
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="200"  v-if="insureMode=='add'">
          <template slot-scope="scope">
            <div class="action-buttons" v-if="!(scope.row.isDefault==1 && scope.row.joinType==1)">
              <el-button class="btn-center" type="text" @click="removeInsureInput(scope.row.id)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>

      </el-table>
    </DtPopup>
  </div>
</template>

<script>
import { validate, validateAlls } from "@/config/validation";
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import Pagination from "@/components/layouts/Pagination";
import { memberList,branchUserList,addTeamMember,removeTeamMember,inviteTeamMember,divisionList,divisionRatioList,addDivisionRatio } from '@/api/workbench/index.js';
import {getDicItemList} from "@/config/tool";
import {
  batchAddOrders, checkManager, deleteOrder, divisionAcceptOrReject,
  editDivisionRatio,
  getCompnayList,
  insureOrderList,
  sendDivisionNotice, updateOrder
} from "../../../api/workbench";


export default {
  name: "memberManage",
  inject: ['opportunityDetailFn', 'opportunityFn', 'enterpriseFn', 'opportunityProcessLogFn'],
  data() {
    return {
      opportunityId: this.$route.query.id,
      editDisable: true,
      // 判断是否有项目二次分工，有数据一次分工就只能查看
      hasSecond: false,
      isManager: false,


      initParam: {
        opportunityId:"1",
        num:1
      },
      /*** 填写保单列表 ***/
      insureCompanyList:[],
      insureMenuListPros: {
        toolTitle: "保单号",
        toolList: [
          {
            name: "填写保单号",
            btnCode: ""
          }
        ]
      },
      insureInputIndex:1,
      insureList:[],
      insureInputData:[],
      showInsurePopup:false,
      insureMode:"add",

      /*** 项目分工列表 ***/
      divisionPopupTitle:"新增项目分工",
      divisionNoticeTitle:"确认项目分工（首次）",
      divisionColumnTitle:{name:"项目分工",ratio:"分配比例"},
      divisionMode:'add',
      divisionNum:0,
      divisionOneData:[],
      divisionTwoData:[],
      divisionRatioList:[],
      divisionNoticeData:[],
      selectDivisionRatio:"",
      divisionOneMenuListPros: {
        toolTitle: "项目分工（首次）",
        toolList: [
          {
            name: "新增项目分工",
            btnCode: ""
          },
          {
            name: "确认分工比例",
            btnCode: ""
          }
        ]
      },
      divisionTwoMenuListPros: {
        toolTitle: "项目分工（二次）",
        toolList: [
          {
            name: "新增项目分工",
            btnCode: ""
          },
          {
            name: "确认分工比例",
            btnCode: ""
          }
        ]
      },
      divisionFormRules:{
        divisionId: [{required: true, validator: validate, trigger: "blur"}],
        userId: [{required: true, validator: validate, trigger: "blur"}],
        divisionRatio: [{required: true, validator: validate, trigger: "blur"}]
      },
      showAddDivisionPopup: false,
      showViewDivisionPopup:false,
      showDivisionNoticePopup:false,
      addDivisionForm:{
        id:null,
        divisionId:"",
        userId:"",
        divisionRatio:"",
        remark:""
      },
      viewDivisionForm:{},

      /*** 项目成员列表 ***/
      // 表格数据
      memberData: [],
      showInivitePopup: false,
      inviteMember:{},

      /*** 项目成员新增弹窗 ***/
      addMemberSelectRow: null,
      addMemberMenuListPros: {
        toolTitle: "项目成员",
        toolList: [
          {
            name: "新增项目成员",
            btnCode: ""
          }
        ]
      },
      showAddMemberPopup: false,
      branchUserData:[],
      // userTotal:0,
      addMemberParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          nickName: "",
          roleType:"1"
        }
      },
      addMemberFormTemp: [
        {
          label: "人员角色",
          name: "roleType",
          type: "select",
          placeholder: "请输入人员姓名",
          list: [],
          fixedShow: true,
          tempShow: true,
        },
        {
          label: "人员姓名",
          name: "nickName",
          type: "input",
          placeholder: "请输入人员姓名"
        }
      ],
      // total: 0,
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  filters: {
    // 多个被保人用逗号隔开显示
    getJoinType(val) {
      if(val){
        if(val.joinType ==1 && val.isDefault==1){
          return "默认已确认";
        }

        if(val.joinType ==0){
          return "待确认"
        }
        if(val.joinType ==1){
          return "已确认"
        }
        if(val.joinType ==5){
          return "已拒绝"
        }
      }
      return "";
    },
    getDivisionStatus(val) {
      // console.log(val);
      if(val){
        if(val.status ==1 && val.isDefault==1){
          return "默认已确认";
        }

        if(val.status ==0){
          return "待确认"
        }
        if(val.status ==1){
          return "已确认"
        }
        if(val.status ==5){
          return "已拒绝"
        }
      }
      return "";
    }
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 从父组件获取注入的数据
    injectedOpportunityDetail() {
      return this.opportunityDetailFn ? this.opportunityDetailFn() : null;
    },
    injectedOpportunity() {
      return this.opportunityFn ? this.opportunityFn() : null;
    },
    injectedEnterprise() {
      return this.enterpriseFn ? this.enterpriseFn() : null;
    },
    injectedOpportunityProcessLog() {
      return this.opportunityProcessLogFn ? this.opportunityProcessLogFn() : null;
    }
  },
  async created() {
    this.initData();
    this.editStatusCheck();
  },
  methods: {

    // 根据机会状态判断是否能编辑
    editStatusCheck(){
      if(this.injectedOpportunityDetail  && this.injectedOpportunityDetail.opportunity
        && this.injectedOpportunityDetail.opportunity.status && this.injectedOpportunityDetail.opportunity.status==1){
        this.editDisable = false;
      } else {
        this.editDisable = true;
      }

      if(this.editDisable){
        this.addMemberMenuListPros.toolList=[];
        this.divisionOneMenuListPros.toolList=[];
        this.divisionTwoMenuListPros.toolList=[];
        this.insureMenuListPros.toolList=[];
      }

    },

    async initData() {
      this.checkManager();
      this.getMemberData();
      this.getMemberDictList();
      this.getDivisionList(1);
      this.getDivisionList(2);
      this.getDivisionRatioList();
      this.getInsureCompanyList();
      this.getInsureOrders();

    },

    async checkManager() {
      let _param = this.initParam;
      _param.opportunityId = this.opportunityId;
      const res = await checkManager(_param);
      console.log("checkManager >>"+ res);
      this.isManager =res;
      if(!this.isManager){
        this.addMemberMenuListPros.toolList=[];
        this.divisionOneMenuListPros.toolList=[];
        this.divisionTwoMenuListPros.toolList=[];
        this.insureMenuListPros.toolList=[];
      }
    },



    /****** 填写保单号 ******/
    opendEditInsureOrder(row){
      this.insureMode="edit";
      this.insureInputData=[];
      this.insureInputData.push({id:row.id,companyCode:row.companyCode,policyNo:row.policyNo,opportunityId:this.opportunityId});
      this.showInsurePopup=true;
    },
    confirmRemoveOrder(row){
      this.$confirm(`确定要删除类别"${row.policyNo}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.removeInsureOrder(row.id);
      }).catch(() => {
        // 用户取消删除
      })

    },
    async removeInsureOrder(id){
      let param = {id:id};
      const res = await deleteOrder(param);
      if(res){
        this.$message.success(res.resp_msg);
        this.getInsureOrders();
      }
    },

    async getInsureOrders(){
      // console.log(this.insureInputData);
      let param = {opportunityId:this.opportunityId};
      const res = await insureOrderList(param);
      if(res && !res.resp_msg){
        this.insureList=res;
      }
    },

    saveInsurePolicyNo(){
      // console.log(this.insureInputData);

      if(this.insureMode=='add'){
        this.addInsureOrder();
      } else if(this.insureMode=='edit'){
        this.updateInsureOrder();
      }

      this.showInsurePopup=false;
    },
    async updateInsureOrder(){
      if(this.insureInputData && this.insureInputData.length>0){
        this.insureInputData.forEach(obj => {
          if(this.insureCompanyList && this.insureCompanyList.length>0){
            this.insureCompanyList.forEach(item => {
              if(item.comCode == obj.companyCode){
                obj.companyName=item.comFullName;
              }
            })
          }
        });
        // console.log(this.insureInputData);

        let orders = _.cloneDeep(this.insureInputData[0]);
        const res = await updateOrder(orders);
        if(res){
          this.$message.success("操作成功");
          this.getInsureOrders();
        }
      }
    },
    async addInsureOrder(){
      if(this.insureInputData && this.insureInputData.length>0){
        this.insureInputData.forEach(obj => {
          if(this.insureCompanyList && this.insureCompanyList.length>0){
            this.insureCompanyList.forEach(item => {
              if(item.comCode == obj.companyCode){
                obj.companyName=item.comFullName;
              }
            })
          }
        });
        // console.log(this.insureInputData);

        let orders = _.cloneDeep(this.insureInputData);
        const res = await batchAddOrders(orders);
        if(res){
          this.$message.success(res.resp_msg);
          this.getInsureOrders();
        }
      }
    },
    async getInsureCompanyList() {
      const res = await getCompnayList();
      if(res){
        this.insureCompanyList = res;
      }
    },
    insureHandleTool(item){
      if (item.name == "填写保单号") {
        this.showInsurePopup=true;
        this.insureInputData=[];
        this.insureMode="add";
      }
    },
    addInsureInputData(){
      this.insureInputIndex +=1;
      this.insureInputData.push({id:this.insureInputIndex,companyCode:"",policyNo:"",opportunityId:this.opportunityId});
    },
    removeInsureInput(id){
      if(this.insureInputData){
        const removeIndex = this.insureInputData.findIndex(item=> item.id == id);
        if(removeIndex!=-1){
          this.insureInputData.splice(removeIndex,1);
        }
      }
    },

    /****** 项目分工列表 ******/
    async divisionAcceptOrReject(id,num) {
      let _param = {id:id,status:1};
      const res = await divisionAcceptOrReject(_param);
      // console.log(res);
      if(res){
        this.getDivisionList(num);
        this.$message.success(res.resp_msg);
      }
    },

    // 查询已添加的项目成员
    async getDivisionRatioList() {

      const res = await divisionRatioList();
      // console.log(res);
      if(res){
        this.divisionRatioList = res;
      }
    },
    async getDivisionList(num) {
      let _param = this.initParam;
      _param.opportunityId = this.opportunityId;
      _param.num=num;
      const res = await divisionList(_param);
      // console.log(res);
      if(res && !res.resp_msg){
        if(num == 1){
          this.divisionOneData = res;
        } else if(num ==2){
          this.divisionTwoData = res;
          if(this.divisionTwoData && this.divisionTwoData.length >0){
            this.hasSecond = true;
            this.divisionOneMenuListPros.toolList=[];
          }

        }
      }
    },
    addOneDivisionHandleTool(item){
      if (item.name == "新增项目分工") {
        this.divisionNum =1;
        this.openAddDivisionPopup();
      }
      if(item.name=="确认分工比例"){
        this.divisionNum =1;
        this.divisionNoticeTitle = "确认项目分工（首次）";
        this.divisionColumnTitle={name:"项目分工",ratio:"分配比例"};
        this.openNoticeDivisionPopup();
      }
    },
    addTwoDivisionHandleTool(item){
      if (item.name == "新增项目分工") {
        this.divisionNum =2;
        this.openAddDivisionPopup();
      }
      if(item.name=="确认分工比例"){
        this.divisionNum =2;
        this.divisionNoticeTitle = "确认项目分工（二次）";
        this.divisionColumnTitle={name:"二次项目分工",ratio:"二次分配比例"};
        this.openNoticeDivisionPopup();
      }
    },
    openAddDivisionPopup() {
      this.addDivisionForm={
        id:null,
          divisionId:"",
          userId:"",
          divisionRatio:"",
          remark:""
      };

      this.divisionPopupTitle="新增项目分工";
      this.divisionMode='add';
      this.showAddDivisionPopup = true;

    },
    handleDivisionChange(value){
      console.log(value);
      if(this.divisionRatioList){
        this.divisionRatioList.forEach(obj => {
          if(obj.id == value){
            this.selectDivisionRatio=obj.ratio;
          }
        });
      }
    },
    addDivision(){
      if (!validateAlls(this.$refs.addDivisionForm)) return;

      let selRatio = parseInt(this.addDivisionForm.divisionRatio);
      let limit = parseInt(this.selectDivisionRatio);

      if(selRatio>limit){
        this.$message.error("输入的比例不能超过比例上限");
        return;
      }
      if(this.divisionMode == 'add'){
        this.saveDivisionRatio();
      } else {
        this.updateDivisionRatio();
      }
    },
    async saveDivisionRatio(){
      let _param = {
        opportunityId:this.opportunityId,
        userId:this.addDivisionForm.userId,
        tenantId:this.$store.state.layoutStore.currentLoginUser.tenantId,
        divisionId:this.addDivisionForm.divisionId,
        divisionRatio:this.addDivisionForm.divisionRatio,
        remark:this.addDivisionForm.remark,
        num:this.divisionNum
      };

      const res = await addDivisionRatio(_param);
      if(res){
        this.$message.success(res.resp_msg);
        this.getDivisionList(this.divisionNum);
        this.showAddDivisionPopup = false;

      }
    },
    async updateDivisionRatio(){
      let _param = {
        id:this.addDivisionForm.id,
        opportunityId:this.opportunityId,
        userId:this.addDivisionForm.userId,
        tenantId:this.$store.state.layoutStore.currentLoginUser.tenantId,
        divisionId:this.addDivisionForm.divisionId,
        divisionRatio:this.addDivisionForm.divisionRatio,
        remark:this.addDivisionForm.remark,
        num:this.divisionNum
      };

      const res = await editDivisionRatio(_param);
      if(res){
        this.$message.success(res.resp_msg);
        console.log(this.divisionNum);
        this.getDivisionList(this.divisionNum);
        this.showAddDivisionPopup = false;
      }
    },

    openViewDivisionPopup(row) {
      this.showViewDivisionPopup = true;
      this.viewDivisionForm=row;
    },
    openEditDivisionPopup(row,num) {
      this.divisionNum=num;
      this.divisionPopupTitle="编辑项目分工";
      this.divisionMode='edit';
      let _param = _.cloneDeep(row);
      this.addDivisionForm=_param;
      this.showAddDivisionPopup = true;
    },
    openNoticeDivisionPopup(){
      this.showDivisionNoticePopup = true;
      if(this.divisionNum ==1){
        let oneData = _.cloneDeep(this.divisionOneData);
        this.divisionNoticeData = oneData.filter(item => item.isDefault !== 1);
      } else if(this.divisionNum == 2){
        let twoData = _.cloneDeep(this.divisionTwoData);
        this.divisionNoticeData = twoData.filter(item => item.isDefault !== 1);
      }

    },
    async sendDivisionNoticeConfirm(){
      let _param = {
        opportunityId:this.opportunityId,
        num:this.divisionNum
      };
      const res =await sendDivisionNotice(_param);
      if(res){
        this.$message.success(res.resp_msg);
        this.showDivisionNoticePopup = false;
      }
    },


    /****** 项目成员列表 ******/
    addMemberHandleTool(item){
      if (item.name == "新增项目成员") {
        this.openAddMemberPopup();
      }
    },


    // 删除项目成员
    async removeMember(id) {
      this.$confirm(`会连同项目分工一起删除，是否确认？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.sureRemoveMember(id);
      }).catch(() => {
        // 用户取消删除
      })
    },
    async sureRemoveMember(id){
      let _param = {id:id};
      const res = await removeTeamMember(_param);
      if(res){
        this.$message.success(res.resp_msg);
        this.getMemberData();
        this.getDivisionList(1);
        this.getDivisionList(2);
      }
    },

    // 邀请确认
    inviteConfirm(obj){
      this.inviteMember=obj
      this.showInivitePopup = true;
    },
    // 发送确认邀请
    async inviteConfirmSend(){
      const res = await inviteTeamMember(this.inviteMember);
      if(res){
        this.$message.success(res.resp_msg);
        this.getMemberData();
        this.showInivitePopup = false;
      }
    },
    // 查询已添加的项目成员
    async getMemberData() {
      let _param = this.initParam;
      _param.opportunityId = this.opportunityId;
      const res = await memberList(_param);
      // console.log(res);
      if(res){
        this.memberData = res;
      }
    },

    /****** 项目成员新增弹窗 ******/
    addMemberSelect(row) {
      this.addMemberSelectRow = row;
      // console.log("addMemberSelect");
      // console.log(row);
    },

    async getMemberDictList(){
      const response = await getDicItemList("elms.role.type");
      // console.log("11 >>"+response);

      this.addMemberFormTemp.forEach((item) => {
        if (item.name == "roleType") {
          item.list = response;
        }
      });
    },
    // 获取可添加的项目成员
    async getBranchUserDataData() {
      let _param = this.addMemberParam.param;
      const res = await branchUserList(_param);
      // console.log(res);
      if(res){
        this.branchUserData=res;

      }

    },
    openAddMemberPopup() {
      this.getBranchUserDataData();
      this.showAddMemberPopup = true;
    },
    addMemberConfirm(){
      if(!this.addMemberSelectRow){
        this.$message.error("请选择要添加测项目成员");
      } else {
        this.addMember();
      }
    },
    async addMember(){
      // 新增项目成员
      let member ={opportunityId:this.opportunityId,
        roleType:this.addMemberSelectRow.roleType,
        userId:this.addMemberSelectRow.userId};
      const res =await addTeamMember(member);
      if(res){
        this.$message.success(res.resp_msg);
      }
      this.getMemberData();
      this.showAddMemberPopup = false;
    },
    closeAddMemberPopup() {
      this.showAddMemberPopup = false;
    },
    // 搜索
    addMemberNormalSearch(data) {
      this.getBranchUserDataData();
    },
    // 重置
    addMemberNormalResetQuery() {
      this.addMemberParam.param.roleType="1";
      this.addMemberParam.param.nickName="";
      this.getBranchUserDataData();
    }



    // addMemberHandleSizeChange(val) {
    //   this.initParam.pageSize = val;
    //   this.initData();
    // },

    // addMemberHandleCurrentChange(val) {
    //   this.initParam.pageNum = val;
    //   this.initData();
    // },






  }
};
</script>

<style lang="less" scoped>

.location-box {
  width: 360px;
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  cursor: pointer;
  margin: 5px 5px 5px 5px;

  .left {
    cursor: pointer;

    .location {
      margin: 5px 5px 5px 5px;
    }
  }

  .right {
    cursor: pointer;
  }
}
.member-manage {
  .text-content{
    width: 100%;
    text-align: left;
  }
  .text-content div{
    margin: 5px 5px 5px 5px;
  }

  .communication-item {
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .log-form {
    padding-bottom: 20px;
    height: 650px; /* 设置弹窗固定高度 */
    display: flex;
    flex-direction: column;

    .form-content {
      flex: 1;
      overflow-y: auto; /* 添加滚动条 */
      padding-right: 10px; /* 为滚动条留出空间 */
      margin-bottom: 20px;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      /* Firefox 隐藏滚动条 */
      scrollbar-width: none;

      /* IE 隐藏滚动条 */
      -ms-overflow-style: none;
    }

    .upload-section {
      margin-top: 10px;

      .upload-area {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: border-color 0.3s;

        .upload-placeholder {
          color: #8c939d;
          font-size: 14px;

          i {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
          }
        }
      }

      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;

        .image-item {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e4e7ed;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .image-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background: rgba(0, 0, 0, 0.8);
            }

            i {
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }
    }

    .form-actions {
      flex-shrink: 0; /* 防止按钮区域被压缩 */
      padding-top: 20px;
      text-align: center;
      border-top: 1px solid #e4e7ed;
      background: #fff;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

</style>
