<template>
  <div class="project-log">
    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>
    <!-- <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"></SearchForm> -->



    <!-- 项目日志表格 -->
    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover row-key="id">
      <el-table-column align="center" prop="logDesc" label="沟通事项" >
        <template slot-scope="scope">
          <span class="communication-item">{{ scope.row.logDesc }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="meetingDate" label="沟通日期" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.meetingDate }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="recorderName" label="记录人" >
        <template slot-scope="scope">
          <span>{{ scope.row.recorderName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="recordTime" label="记录时间" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.recordTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="280" fixed="right">
        <template slot-scope="scope">
          <div class="action-buttons">
            <el-button class="btn-center" type="text" @click="viewLog(scope.row)">
              查看
            </el-button>
            <el-button class="btn-center" type="text" @click="editLog(scope.row)">
              编辑
            </el-button>
            <el-button class="btn-center" type="text" @click="deleteLog(scope.row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
      :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

    <!-- 新增/编辑/查看项目日志弹窗 -->
    <DtPopup :isShow.sync="showLogPopup" @close="closeLogPopup"
      :title="getPopupTitle()" center :footer="false" width="700px">
      <div class="log-form">
        <div class="form-content">
          <el-form ref="logFormRef" :model="logForm" :rules="logRules" label-width="120px">
            <el-form-item label="沟通事项" class="dt-input-big" prop="logDesc">
              <el-input v-model="logForm.logDesc" placeholder="请输入本次日志要记录的事项 (0/20字)" 
                maxlength="20" show-word-limit :disabled="isViewMode"></el-input>
            </el-form-item>
            <el-form-item label="记录人" class="dt-input-big" prop="recorderName">
              <el-input v-model="logForm.recorderName" placeholder="记录人姓名" disabled></el-input>
            </el-form-item>
            <el-form-item label="沟通日期" class="dt-input-big" prop="meetingDate">
              <el-date-picker v-model="logForm.meetingDate" type="date" placeholder="请选择沟通日期年/月/日"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%" :disabled="isViewMode">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="项目组响应" class="dt-input-big">
              <el-input type="textarea" v-model="logForm.meetingContent" placeholder="项目组响应内容 (选填)"
                :rows="4" maxlength="1000" show-word-limit :disabled="isViewMode"></el-input>
              <div class="upload-section">
                <div class="image-list" v-if="meetingImageUrl.length > 0">
                  <div class="image-item" v-for="(image, index) in meetingImageUrl" :key="index">
                    <img :src="image" :alt="'图片' + (index + 1)" @click="previewImage(image)" />
                    <div class="image-actions" v-if="!isViewMode">
                      <i class="el-icon-delete" @click="removeImage('project', index)"></i>
                    </div>
                  </div>
                </div>
                <div class="upload-area" v-if="!isViewMode" @click="handleUpload('project')">
                  <div class="upload-placeholder">
                    <i class="el-icon-picture"></i>
                    <span>上传图片({{ meetingImageUrl.length }}/10)</span>
                  </div>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="顾问/客户需求" class="dt-input-big">
              <el-input type="textarea" v-model="logForm.customerDemand" placeholder="顾问和客户的需求 (选填)"
                :rows="4" maxlength="1000" show-word-limit :disabled="isViewMode"></el-input>
              <div class="upload-section">
                <div class="image-list" v-if="demandImageUrl.length > 0">
                  <div class="image-item" v-for="(image, index) in demandImageUrl" :key="index">
                    <img :src="image" :alt="'图片' + (index + 1)" @click="previewImage(image)" />
                    <div class="image-actions" v-if="!isViewMode">
                      <i class="el-icon-delete" @click="removeImage('consultant', index)"></i>
                    </div>
                  </div>
                </div>
                <div class="upload-area" v-if="!isViewMode" @click="handleUpload('consultant')">
                  <div class="upload-placeholder">
                    <i class="el-icon-picture"></i>
                    <span>上传图片({{ demandImageUrl.length }}/10)</span>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="form-actions" v-if="!isViewMode">
          <el-button @click="closeLogPopup">取消</el-button>
          <el-button type="primary" @click="submitLogForm" :loading="submitLoading">
            {{ logForm.id ? '保存' : '确认' }}
          </el-button>
        </div>
        <div class="form-actions" v-else>
          <el-button @click="closeLogPopup">关闭</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 删除确认弹窗 -->
    <DtPopup :isShow.sync="showDeletePopup" @close="closeDeletePopup" title="确认删除" center :footer="false" width="500px">
      <div class="delete-confirm">
        <div class="confirm-content">
          <p>删除后无法恢复，请确认是否删除该项目日志？</p>
        </div>
        <div class="confirm-actions">
          <el-button @click="closeDeletePopup">取消</el-button>
          <el-button :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color }" @click="confirmDelete" :loading="deleteLoading">删除</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 图片预览弹窗 -->
    <DtPopup :isShow.sync="showImagePreview" @close="closeImagePreview" title="图片预览" center :footer="false" width="800px">
      <div class="image-preview">
        <div class="preview-content">
          <img :src="previewImageUrl" :alt="'预览图片'" class="preview-image" />
        </div>
        <div class="preview-actions">
          <el-button @click="closeImagePreview">关闭</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 隐藏的文件上传输入框 -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="onFileChange"
    />
  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import Pagination from "@/components/layouts/Pagination";
import { addOpportunityLog, deleteOpportunityLog,uploadFile, getOpportunityLog, updateOpportunityLog, getOpportunityLogList } from "@/api/workbench";
import http from "@/utils/httpService";
import { rootPath } from "@/utils/globalParam";


export default {
  name: "projectLog",
  inject: ['opportunityDetailFn', 'opportunityFn', 'enterpriseFn', 'opportunityProcessLogFn'],
  data() {
    return {
      toolListProps: {
        toolTitle: "项目日志管理",
        toolList: [
          {
            name: "新增项目日志",
            icon: "el-icon-plus",
            btnCode: ""
          }
        ]
      },
      // 表格数据
      tableData: [
        
      ],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          opportunityId: this.$route.query.id||"CHANCE987654"
        }
      },
      // searchFormTemp: [
      //   {
      //     label: "沟通事项",
      //     name: "logDesc",
      //     type: "input",
      //     placeholder: "请输入沟通事项"
      //   },
      //   {
      //     label: "记录人",
      //     name: "recorderName",
      //     type: "input",
      //     placeholder: "请输入记录人"
      //   },
      // ],
      total: 0,

      // 日志表单相关
      showLogPopup: false,
      isViewMode: false, // 是否为查看模式
      logForm: {
        opportunityId: this.$route.query.id,
        logDesc: "",
        recorderName: "",
        meetingDate: "",
        meetingContent: "",
        customerDemand: ""
      },
      logRules: {
        logDesc: [
          { required: true, message: "请输入沟通事项", trigger: "blur" },
          { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" }
        ],
        // recorderName: [
        //   { required: true, message: "请输入记录人", trigger: "blur" }
        // ],
        meetingDate: [
          { required: true, message: "请选择沟通日期", trigger: "change" }
        ]
      },
      submitLoading: false,
      meetingImageUrl: [],
      demandImageUrl: [],
      currentUploadType: '', // 当前上传类型：project 或 consultant

      // 删除相关
      showDeletePopup: false,
      deleteItem: {},
      deleteLoading: false,

      // 图片预览相关
      showImagePreview: false,
      previewImageUrl: ''
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup,
    Pagination
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 从父组件获取注入的数据
    injectedOpportunityDetail() {
      return this.opportunityDetailFn ? this.opportunityDetailFn() : null;
    },
    injectedOpportunity() {
      return this.opportunityFn ? this.opportunityFn() : null;
    },
    injectedEnterprise() {
      return this.enterpriseFn ? this.enterpriseFn() : null;
    },
    injectedOpportunityProcessLog() {
      return this.opportunityProcessLogFn ? this.opportunityProcessLogFn() : null;
    }
  },
  async created() {
    this.initData();
  },
  methods: {
    async initData() {
      let res = await getOpportunityLogList(this.initParam);
      if(res) { 
        this.tableData = res.list;
        this.total = res.total;
      }
    },

    // 获取弹窗标题
    getPopupTitle() {
      if (this.isViewMode) {
        return '查看项目日志';
      }
      return this.logForm.id ? '编辑项目日志' : '添加项目日志';
    },

    handleTool(item) {
      if (item.name === "新增项目日志") {
        this.addLog();
      }
    },

    // 新增日志
    addLog() {
      this.isViewMode = false;
      this.logForm = {
        id: "",
        opportunityId: this.$route.query.id||"CHANCE987654",
        logDesc: "",
        recorderName: this.$store.state.layoutStore.currentLoginUser.userName,
        meetingDate: "",
        meetingContent: "",
        customerDemand: ""
      };
      this.meetingImageUrl = [];
      this.demandImageUrl = [];
      this.showLogPopup = true;
    },

    // 编辑日志
    async editLog(row) {
      this.isViewMode = false;
      let res = await getOpportunityLog({ id: row.id });
      if(res) {
        this.logForm = res;
        // 回显时去掉时分秒
        if (this.logForm.meetingDate) {
          this.logForm.meetingDate = this.logForm.meetingDate.split(' ')[0];
        }
        this.meetingImageUrl = res.meetingImageUrl || [];
        this.demandImageUrl = res.demandImageUrl || [];
      }
      this.showLogPopup = true;
    },

    // 查看日志
    async viewLog(row) {
      let res = await getOpportunityLog({ id: row.id });
      if(res) {
        this.logForm = res;
        // 回显时去掉时分秒
        if (this.logForm.meetingDate) {
          this.logForm.meetingDate = this.logForm.meetingDate.split(' ')[0];
        }
        this.meetingImageUrl = res.meetingImageUrl || [];
        this.demandImageUrl = res.demandImageUrl || [];
      }
      this.isViewMode = true;
      this.showLogPopup = true;
    },

    // 处理图片上传
    handleUpload(type) {
      this.currentUploadType = type;
      const maxImages = 10;
      const currentImages = type === 'project' ? this.meetingImageUrl : this.demandImageUrl;
      
      if (currentImages.length >= maxImages) {
        this.$message.warning(`最多只能上传${maxImages}张图片`);
        return;
      }
      
      this.$refs.fileInput.click();
    },

    // 文件选择变化
    async onFileChange(event) {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      const maxImages = 10;
      const currentImages = this.currentUploadType === 'project' ? this.meetingImageUrl : this.demandImageUrl;
      const remainingSlots = maxImages - currentImages.length;
      
      if (files.length > remainingSlots) {
        this.$message.warning(`最多只能再上传${remainingSlots}张图片`);
        return;
      }

      // 显示上传进度
      this.$message.info('正在上传图片，请稍候...');

      try {
        for (const file of Array.from(files)) {
          if (file.type.startsWith('image/')) {

            const uploadResult = await uploadFile({fileType:"log",file:file});
            
            if (uploadResult ) {
              const imageData = uploadResult;
              
              if (this.currentUploadType === 'project') {
                this.meetingImageUrl.push(imageData);
              } else {
                this.demandImageUrl.push(imageData);
              }
            }
          } else {
            this.$message.error('只能上传图片文件');
          }
        }
        
        this.$message.success('图片上传完成');
      } catch (error) {
        this.$message.error('图片上传失败，请重试');
      }

      // 清空文件输入框
      event.target.value = '';
    },


    // 删除图片
    removeImage(type, index) {
      if (type === 'project') {
        this.meetingImageUrl.splice(index, 1);
      } else {
        this.demandImageUrl.splice(index, 1);
      }
    },

    // 预览图片
    previewImage(url) {
      this.previewImageUrl = url;
      this.showImagePreview = true;
    },

    // 关闭图片预览弹窗
    closeImagePreview() {
      this.showImagePreview = false;
      this.previewImageUrl = '';
    },


    // 删除日志
    deleteLog(row) {
      this.deleteItem = { ...row };
      this.showDeletePopup = true;
    },

    // 提交日志表单
    async submitLogForm() {
      try {
        await this.$refs.logFormRef.validate();
        this.submitLoading = true;
        let res;
        this.logForm.meetingImageUrl = this.meetingImageUrl
        this.logForm.demandImageUrl = this.demandImageUrl
        
        // 提交时拼接时分秒
        if (this.logForm.meetingDate) {
          this.logForm.meetingDate = this.logForm.meetingDate + ' 00:00:00';
        }
        
        if(this.logForm.id) {
          res = await updateOpportunityLog(this.logForm);
        } else {
          res = await addOpportunityLog(this.logForm);
        }
        if(res) {
          if(this.logForm.id) { 
            this.$message.success("项目日志编辑成功");
          } else {
            this.$message.success("项目日志新增成功");
          }
        }

        this.closeLogPopup();
        this.initData();
      } catch (error) {
        console.error("表单验证失败:", error);
      } finally {
        this.submitLoading = false;
      }
    },

    // 确认删除
    async confirmDelete() {
      try {
        this.deleteLoading = true;
        let res = await deleteOpportunityLog({ id: this.deleteItem.id }  );
        this.closeDeletePopup();
        this.initData();
      } catch (error) {
        this.$message.error("删除失败");
      } finally {
        this.deleteLoading = false;
      }
    },

    // 关闭日志弹窗
    closeLogPopup() {
      this.showLogPopup = false;
      this.isViewMode = false;
      this.$nextTick(() => {
        if (this.$refs.logFormRef) {
          this.$refs.logFormRef.resetFields();
        }
      });
    },

    // 关闭删除弹窗
    closeDeletePopup() {
      this.showDeletePopup = false;
      this.deleteItem = {};
    },

    // 搜索
    normalSearch(data) {
      console.log("搜索参数:", data);
      this.initData();
    },

    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },

    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },

    handleCurrentChange(val) {
              this.initParam.pageNum = val;
        this.initData();
      }
  }
};
</script>

<style lang="less" scoped>
.project-log {
  .communication-item {
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .log-form {
    padding-bottom: 20px;
    height: 650px; /* 设置弹窗固定高度 */
    display: flex;
    flex-direction: column;

    .form-content {
      flex: 1;
      overflow-y: auto; /* 添加滚动条 */
      padding-right: 10px; /* 为滚动条留出空间 */
      margin-bottom: 20px;
      
      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }
      
      /* Firefox 隐藏滚动条 */
      scrollbar-width: none;
      
      /* IE 隐藏滚动条 */
      -ms-overflow-style: none;
    }

    .upload-section {
      margin-top: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      align-items: flex-start;
      
      .upload-area {
        width: 80px;
        height: 80px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: border-color 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        &:hover {
          border-color: #409eff;
          background: #f0f9ff;
        }

        .upload-placeholder {
          color: #8c939d;
          font-size: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;

          i {
            font-size: 20px;
            margin-bottom: 4px;
            display: block;
          }

          span {
            line-height: 1.2;
            word-break: break-all;
          }
        }
      }

      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .image-item {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e4e7ed;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            transition: opacity 0.3s;

            &:hover {
              opacity: 0.8;
            }
          }

          .image-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background: rgba(0, 0, 0, 0.8);
            }

            i {
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }
    }

    .form-actions {
      flex-shrink: 0; /* 防止按钮区域被压缩 */
      padding-top: 20px;
      text-align: center;
      border-top: 1px solid #e4e7ed;
      background: #fff;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  .image-preview {
    padding: 20px;
    text-align: center;

    .preview-content {
      width: 100%;
      height: 600px; /* 限制图片最大高度 */
      display: flex;
      justify-content: center;
      align-items: center;

      .preview-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }

    .preview-actions {
      margin-top: 20px;
      text-align: center;
    }
  }
}

</style> 