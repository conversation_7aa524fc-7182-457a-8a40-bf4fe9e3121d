<template>
  <div class="category-list-container">
    <EditPageContainer
      :title="pageTitle"
      :icon="pageIcon"
      :breadcrumb-items="breadcrumbItems"
      :loading="loading"
      :isView="true"
      @back="handleBack"
      @breadcrumb-click="handleBreadcrumbClick"
    >
      <div class="category-list-content">
         <!-- 操作栏 -->
         <div class="action-bar">
           <div class="left-actions">
             <span class="total-count">共 {{ categoryList.length }} 个核心类别</span>
           </div>
           <div class="right-actions">
             <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增类别</el-button>
           </div>
         </div>

         <!-- 使用UniversalTable组件 -->
        <UniversalTable
           :show-header="false"
           :table-data="categoryList"
           :loading="loading"
           :columns="tableColumns"
           :actions="tableActions"
           :pagination-data="pagination"
           :total="pagination.total"
           :show-search-form="false"
           :action-column-width="340"
           empty-title="暂无核心类别数据"
            empty-description="暂时没有相关数据"
            @size-change="handleSizeChange"
           @current-change="handleCurrentChange"
           @action-click="handleAction"
         >
          <!-- 关联评分项列插槽 -->
          <template #scoreItems="{ row }">
            <div class="score-items">
              <!-- 优先使用后端返回的评分项名称列表 -->
              <template v-if="row.scoreItemNames && row.scoreItemNames.length > 0">
                <el-tag
                  v-for="(itemName, index) in row.scoreItemNames"
                  :key="index"
                  size="small"
                  type="info"
                  style="margin-right: 4px; margin-bottom: 4px;"
                >
                  {{ itemName }}
                </el-tag>
              </template>
              <!-- 回退方案：使用评分项ID和mock数据 -->
              <template v-else-if="row.scoreItems && row.scoreItems.length > 0">
                <el-tag
                  v-for="itemId in row.scoreItems"
                  :key="itemId"
                  size="small"
                  type="info"
                  style="margin-right: 4px; margin-bottom: 4px;"
                >
                  {{ getScoreItemName(itemId) }}
                </el-tag>
              </template>
              <!-- 无关联评分项时的显示 -->
              <span v-else class="empty-text">
                暂无
              </span>
            </div>
          </template>

          <!-- 计算方式列插槽 -->
          <template #calculationMethod="{ row }">
            <el-tag :type="row.calculationMethod === 'sum' ? 'success' : 'warning'" size="small">
              {{ row.calculationMethod === 'sum' ? '求和' : '平均值' }}
            </el-tag>
          </template>

          <!-- 创建时间列插槽 -->
          <template #createTime="{ row }">
            <span>{{ formatDate(row.createTime) }}</span>
          </template>
        </UniversalTable>
      </div>
    </EditPageContainer>
  </div>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import { mockScoreItemList } from '@/mock'
import { getCategoryListByMatrixId, deleteSingleCategory } from '@/api/riskMatrix'

export default {
  name: 'CategoryList',
  components: {
    EditPageContainer,
    UniversalTable
  },
  data() {
    return {
      loading: false,
      matrixId: null,
      enterpriseType: null,
      matrixInfo: {},
      categoryList: [],
      scoreItemOptions: [],
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      tableColumns: [
        {
          prop: 'name',
          label: '类别名称',
          minWidth: 200,
          align: 'center'
        },
        {
          prop: 'scoreItems',
          label: '关联评分项',
          minWidth: 250,
          align: 'center',
          slot: true,
          // 添加formatter作为备用方案
          formatter: (row) => {
            if (row.scoreItemNames && row.scoreItemNames.length > 0) {
              return row.scoreItemNames.join(', ')
            } else if (row.scoreItems && row.scoreItems.length > 0) {
              return row.scoreItems.map(id => this.getScoreItemName(id)).join(', ')
            }
            return '暂无'
          }
        },
        {
          prop: 'calculationMethod',
          label: '计算方式',
          width: 120,
          align: 'center',
          slot: true
        },
        {
          prop: 'levelCount',
          label: '档次数量',
          width: 120,
          align: 'center',
          slot: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center',
          slot: true
        }
      ],
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          type: 'text',
          class: 'action-btn view-btn'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          type: 'text',
          class: 'action-btn edit-btn'
        },
        {
          key: 'configLevels',
          label: '配置档次',
          icon: 'el-icon-s-grid',
          type: 'text',
          class: 'action-btn config-btn'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          type: 'text',
          class: 'action-btn delete-btn'
        }
      ]
    }
  },
  computed: {
    pageTitle() {
      return `核心类别管理 - ${this.matrixInfo.name || '风险矩阵'}`
    },
    pageIcon() {
      return 'el-icon-s-operation'
    },
    breadcrumbItems() {
      return [
        { text: '风险矩阵管理', icon: 'el-icon-s-grid', to: { name: 'riskMatrixList' } },
        { text: '核心类别管理', icon: 'el-icon-s-operation' }
      ]
    }
  },
  created() {
    this.matrixId = this.$route.params.matrixId
    this.enterpriseType = this.$route.params.enterpriseType
    this.loadData()
  },
  methods: {
    async loadData() {
      if (!this.matrixId) {
        this.$message.error('缺少风险矩阵ID')
        return
      }

      this.loading = true
      try {
        const categoryResponse = await this.loadCategoryList()

        if (!categoryResponse) {
          this.$message.error('加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    // 加载类别列表（包含关联评分项信息）
    async loadCategoryList() {
      try {
        const response = await getCategoryListByMatrixId(this.matrixId)
        if (response.code === 200 && response.data) {
          this.categoryList = response.data || []

          // 更新分页信息
          this.pagination.total = this.categoryList.length
          return true
        }
        return false
      } catch (error) {
        console.error('加载类别列表失败:', error)
        // 如果新接口失败，回退到原有方式
      }
    },

    getScoreItemName(id) {
      // 优先使用加载的评分项选项
      const option = this.scoreItemOptions.find(option => option.value === String(id))
      if (option) {
        return option.label
      }

      // 回退到mock数据
      const item = mockScoreItemList.find(item => item.id === id)
      return item ? item.name : `评分项${id}`
    },
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    handleAdd() {
      this.$router.push({
        name: 'categoryEdit',
        params: {
          matrixId: this.matrixId,
          categoryId: 'new',
          enterpriseType: this.enterpriseType
        }
      })
    },
    handleView(row) {
      this.$router.push({
        name: 'categoryEdit',
        params: {
          matrixId: this.matrixId,
          categoryId: row.id
        },
        query: {
          mode: 'view'
        }
      })
    },
    handleEdit(row) {
      this.$router.push({
        name: 'categoryEdit',
        params: {
          matrixId: this.matrixId,
          categoryId: row.id,
          enterpriseType: this.enterpriseType
        }
      })
    },
    handleConfigLevels(row) {
      this.$router.push({
        name: 'levelList',
        params: {
          matrixId: this.matrixId,
          categoryId: row.id
        }
      })
    },
    handleDelete(row) {
      this.$confirm(`确定要删除类别"${row.name}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSingleCategory(this.matrixId, row.id).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.loadData()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        })
      }).catch(() => {
        // 用户取消删除
      })
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadData()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadData()
    },
    handleAction({ action, row }) {
      switch (action) {
        case 'view':
          this.handleView(row)
          break
        case 'edit':
          this.handleEdit(row)
          break
        case 'configLevels':
          this.handleConfigLevels(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },
    handleBack() {
      this.$router.push({
        name: 'riskMatrixEdit',
        params: { id: this.matrixId }
      })
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push(item.to)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.category-list-container {
  min-height: 100vh;
  background: #fbf6ee;
}

.category-list-content {

  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .left-actions {
      display: flex;
      align-items: center;

      .total-count {
        color: #606266;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .right-actions {
      display: flex;
      gap: 12px;

      .el-button {
        background: #D7A256;
        border-color: #D7A256;

        &:hover {
          background: #E6B366;
          border-color: #E6B366;
        }
      }
    }
  }

  .category-table-wrapper {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .category-table {
      /deep/ .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #fbf6ee;
            font-weight: 600;
            font-size: 14px;
            color: #2c3e50;
            border-bottom: 1px solid #f7ecdd;
          }
        }
      }

      /deep/ .el-table__body-wrapper {
        .el-table__row {
          transition: all 0.3s ease;

          &:hover {
            background: rgba(215, 162, 86, 0.05) !important;
          }

          td {
            border-bottom: 1px solid #f7ecdd;
            padding: 12px 0;
          }
        }
      }

      .category-name {
        font-weight: 500;
        color: #2c3e50;
      }

      .score-items {
        .score-item-tag {
          margin-right: 4px;
          margin-bottom: 2px;
          background: #e8f4fd;
          border-color: #b3d8ff;
          color: #409eff;
        }

        .no-data {
          color: #999;
          font-size: 12px;
        }
      }

      .level-count {
        color: #666;
        font-size: 13px;
      }

      .el-button--text {
        padding: 0;
        margin-right: 8px;

        &.delete-btn {
          color: #f56c6c;

          &:hover {
            color: #f56c6c;
            background: rgba(245, 108, 108, 0.1);
          }
        }
      }
    }
  }
}
</style>
