<template>
  <div class="form-config-container">
    <UniversalTable
      title="表单配置"
      subtitle="管理和配置H5端表单字段展示方式，支持动态字段管理"
      title-icon="el-icon-document"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="300"
      :search-label-width="'100px'"
      add-button-text="新增配置"
      empty-title="暂无配置数据"
      empty-description="点击上方新增配置按钮开始创建"
      show-selection
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
      @selection-change="handleSelectionChange"
    >
      <template #type="{ row }">
        <el-tag :type="typeEnum[row.type] ? typeEnum[row.type].type : 'info'" size="small">
          {{ typeEnum[row.type] ? typeEnum[row.type].label : '未知' }}
        </el-tag>
      </template>
      <template #status="{ row }">
        <el-tag :type="row.status === '1' ? 'success' : 'danger'" size="small">
          {{ row.status === '1' ? '启用' : '禁用' }}
        </el-tag>
      </template>
      <template #updateTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.updateTime }}
        </div>
      </template>
      <template #fields="{ row }">
        <el-tag type="info" size="small">{{ row.fields ? row.fields.length : 0 }} 个</el-tag>
      </template>
    </UniversalTable>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue';
import { getFormConfigList, deleteFormConfig } from '@/api/basicConfig';

export default {
  name: 'FormConfig',
  components: { UniversalTable },
  data() {
    return {
      // 分类枚举
      typeEnum: {
        '1': { label: '企业信息', type: 'primary' },
        '2': { label: '员福配置', type: 'success' },
        '3': { label: '企业补充信息', type: 'warning' }
      },
      searchForm: {
        param: {
          configName: '',
          configCode: '',
          type: '',
          status: ''
        }
      },
      tableData: [],
      loading: false,
      selectedRows: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      tableColumns: [
        { prop: 'configName', label: '配置名称', minWidth: 180, align: 'center' },
        { prop: 'configCode', label: '配置编码', minWidth: 120, align: 'center' },
        { prop: 'type', label: '分类', width: 100, align: 'center', slot: true },
        { prop: 'status', label: '状态', width: 80, align: 'center', slot: true },
        { prop: 'remark', label: '描述', minWidth: 200, align: 'center' },
        { prop: 'updateTime', label: '更新时间', width: 180, align: 'center', slot: true }
      ],
      tableActions: [
        { key: 'config', label: '配置', icon: 'el-icon-setting', class: 'config-btn', size: 'mini' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn', size: 'mini' }
      ],
      searchFormConfig: [
        { label: '配置名称', name: 'configName', type: 'input', placeholder: '请输入配置名称' },
        { label: '配置编码', name: 'configCode', type: 'input', placeholder: '请输入配置编码' },
        {
          label: '分类',
          name: 'type',
          type: 'select',
          placeholder: '请选择分类',
          options: [
            { label: '企业信息', value: '1' },
            { label: '员福配置', value: '2' },
            { label: '企业补充信息', value: '3' }
          ]
        },
        {
          label: '状态',
          name: 'status',
          type: 'select',
          placeholder: '请选择状态',
          options: [
            { label: '启用', value: '1' },
            { label: '禁用', value: '0' }
          ]
        }
      ]
    };
  },
  mounted() {
    this.loadTableData();
  },
  methods: {
    async loadTableData() {
      this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        };
        const response = await getFormConfigList(params);
        console.log('response1:', response);
        if (response) {
          this.tableData = response.list || [];
          this.pagination.total = response.total || 0;
        }
      } catch (error) {
        this.$message.error('加载数据失败');
        console.error('加载数据失败:', error);
      } finally {
        this.loading = false;
      }
    },
    handleSearch(searchData) {
      this.searchForm = { ...searchData };
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleReset() {
      this.searchForm = { param: { configName: '', configCode: '', type: '', status: '' } };
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleAdd() {
      this.$router.push({ name: 'formConfigEdit' });
    },
    handleAction({ action, row }) {
      if (action === 'config') {
        this.$router.push({ name: 'formConfigEdit', params: { id: row.id } });
      } else if (action === 'delete') {
        this.handleDelete(row);
      }
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.pageNum = 1;
      this.loadTableData();
    },
    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.loadTableData();
    },
    async handleDelete(row) {
      try {
        await this.$confirm(`确定要删除配置\"${row.configName}\"吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        const response = await deleteFormConfig({ id: row.id });
        if (response) {
          this.$message.success('删除成功');
          this.loadTableData();
        } else {
          this.$message.error((response && response.message) || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败');
          console.error('删除失败:', error);
        }
      }
    }
  }
};
</script>

<style scoped>
.form-config-container {
  padding: 20px;
}

.time-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-cell i {
  margin-right: 5px;
  color: #909399;
}
</style>
