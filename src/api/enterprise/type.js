// 企业类型管理相关 API
import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';

// 获取企业类型分页列表
export const getEnterpriseTypeList = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/type/page", data);

// 删除企业类型
export const deleteEnterpriseType = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/type/delete", data);

// 新增企业类型
export const createEnterpriseType = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/type/add", data);

// 更新企业类型
export const updateEnterpriseType = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/type/update", data);

// 获取企业类型详情
export const getEnterpriseTypeDetail = (id) => {
  return http.Axios.post(rootPath + "/api/enterprise/type/detail", { id });
};

// 获取企业类型
export const getEnterpriseTypeOptions = () => {
  return http.Axios.get(rootPath + "/api/enterprise/type/enum");
};


