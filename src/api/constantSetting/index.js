import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';

// 获取常数列表（分页查询）
export const getConstantList = (pageRequest) => {
  return http.Axios.post(rootPath + "/api/constantConfig/list", pageRequest);
};

// 获取常数详情
export const getConstantDetail = (id) => {
  return http.Axios.get(rootPath + `/api/constantConfig/${id}`);
};

// 创建常数
export const createConstant = (data) => {
  return http.Axios.post(rootPath + "/api/constantConfig", data);
};

// 更新常数
export const updateConstant = (id, data) => {
  return http.Axios.put(rootPath + `/api/constantConfig/${id}`, data);
};

// 更新常数状态
export const updateConstantStatus = (id, status) => {
  return http.Axios.put(rootPath + `/api/constantConfig/${id}/status?status=${status}`);
};

// 删除常数
export const deleteConstant = (id) => {
  return http.Axios.delete(rootPath + `/api/constantConfig/${id}`);
};
