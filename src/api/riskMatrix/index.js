import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'
import { getEnterpriseTypeOptions } from '@/api/enterprise/type'

// 获取风险矩阵列表
export const getRiskMatrixList = async (params = {}) => {
    return http.Axios.post(rootPath + '/api/elms/riskMatrix/page', params)
};

// 获取风险矩阵详情
export const getRiskMatrixDetail = async (id) => {
  try {
    const response = await http.Axios.get(rootPath + `/api/elms/riskMatrix/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('获取风险矩阵详情失败:', error)
    throw error
  }
};

// 保存风险矩阵（新增或更新）
export const saveRiskMatrix = async (data) => {
  try {
    const response = await http.Axios.post(rootPath + '/api/elms/riskMatrix/save', data)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('保存风险矩阵失败:', error)
    throw error
  }
};

// 删除风险矩阵
export const deleteRiskMatrix = async (id) => {
  try {
    const response = await http.Axios.delete(rootPath + `/api/elms/riskMatrix/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('删除风险矩阵失败:', error)
    throw error
  }
};

// 获取评分项列表
export const getScoreItemList = async (params = {}) => {
  try {
    const response = await http.Axios.post(rootPath + '/api/elms/scoreItem/page', params)
    return response
  } catch (error) {
    console.error('获取评分项列表失败:', error)
    throw error
  }
};

// 获取评分项详情
export const getScoreItemDetail = async (id) => {
  try {
    const response = await http.Axios.get(rootPath + `/api/elms/scoreItem/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('获取评分项详情失败:', error)
    throw error
  }
};

// 保存评分项（新增或更新）
export const saveScoreItem = async (data) => {
  try {
    const response = await http.Axios.post(rootPath + '/api/elms/scoreItem/save', data)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('保存评分项失败:', error)
    throw error
  }
};

// 删除评分项
export const deleteScoreItem = async (id) => {
  try {
    const response = await http.Axios.delete(rootPath + `/api/elms/scoreItem/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('删除评分项失败:', error)
    throw error
  }
};

// 获取企业类型字典
export const getEnterpriseTypes = async () => {
  // 调用企业类型枚举接口获取真实数据
  const response = await getEnterpriseTypeOptions()

  // 确保返回的数据格式正确
  const data = response && Array.isArray(response) ? response : []

  return {
    code: 200,
    message: "success",
    data: data
  };
};
// 根据类别ID查询档次列表
export const getLevelsByCategoryId = async (matrixId, categoryId) => {
  try {
    const response = await http.Axios.get(rootPath + `/api/elms/riskMatrix/${matrixId}/categories/${categoryId}/levels`)
    console.log('根据类别ID查询档次列表成功:', response)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('根据类别ID查询档次列表失败:', error)
    throw error
  }
};
// 保存类别的档次配置
export const saveLevelsForCategory = async (matrixId, categoryId, levels) => {
  try {
    const response = await http.Axios.post(rootPath + `/api/elms/riskMatrix/${matrixId}/categories/${categoryId}/levels`, levels)
    return response
  } catch (error) {
    console.error('保存档次配置失败:', error)
    throw error
  }
};
/**
 * 分页查询行业风险配置列表
 */
export const getIndustryRiskConfigPage = async (params) => {
  try {
    return http.Axios.post(rootPath + '/api/elms/industryRiskConfig/page', params)
  } catch (error) {
    console.error('分页查询行业风险配置失败:', error)
    throw error
  }
}
/**
 * 根据行业编码获取配置详情
 */
export const getIndustryRiskConfigByCode = async (industryCode, tenantId) => {
  try {
    const params = tenantId ? `?tenantId=${tenantId}` : ''
    const response = await http.Axios.get(rootPath + `/api/elms/industryRiskConfig/${industryCode}${params}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('获取行业风险配置详情失败:', error)
    throw error
  }
}

/**
 * 保存行业风险配置
 */
export const saveIndustryRiskConfig = async (data) => {
  try {
    const response = await http.Axios.post(rootPath + '/api/elms/industryRiskConfig/save', data)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('保存行业风险配置失败:', error)
    throw error
  }
}
/**
 * 删除行业风险配置
 */
export const deleteIndustryRiskConfig = async (id) => {
  try {
    const response = await http.Axios.delete(rootPath + `/api/elms/industryRiskConfig/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('删除行业风险配置失败:', error)
    throw error
  }
}

export const getScoreItemOptions = (data) => {
  return http.Axios.post(rootPath + `/api/elms/scoreItem/options`, data);
};

// ==================== 类别管理API ====================

// 获取风险矩阵类别列表（仅基本信息）
export const getCategoryListByMatrixId = async (matrixId) => {
  try {
    const response = await http.Axios.get(rootPath + `/api/elms/riskMatrix/${matrixId}/categories/list`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('获取类别列表失败:', error)
    throw error
  }
};

// 获取单个类别详情（包含档次配置和关联评分项）
export const getCategoryDetail = async (matrixId, categoryId) => {
  try {
    const response = await http.Axios.get(rootPath + `/api/elms/riskMatrix/${matrixId}/categories/${categoryId}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('获取类别详情失败:', error)
    throw error
  }
};

// 保存单个类别（新增或更新）
export const saveSingleCategory = async (matrixId, categoryData) => {
  try {
    const response = await http.Axios.post(rootPath + `/api/elms/riskMatrix/${matrixId}/categories/save`, categoryData)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('保存单个类别失败:', error)
    throw error
  }
};

// 删除单个类别
export const deleteSingleCategory = async (matrixId, categoryId) => {
  try {
    const response = await http.Axios.delete(rootPath + `/api/elms/riskMatrix/${matrixId}/categories/${categoryId}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('删除类别失败:', error)
    throw error
  }
};
