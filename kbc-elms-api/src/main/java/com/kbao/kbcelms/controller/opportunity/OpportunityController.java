package com.kbao.kbcelms.controller.opportunity;

import com.kbao.commons.enums.ResultStatusEnum;import com.kbao.commons.web.Result;import com.kbao.kbcbsc.log.annotation.LogAnnotation;import com.kbao.kbcelms.common.constants.ElmsConstant;import com.kbao.kbcelms.formConfig.bean.InsureFieldReqVo;import com.kbao.kbcelms.formConfig.bean.OpportunityConfigResVo;import com.kbao.kbcelms.formConfig.model.FormConfigField;import com.kbao.kbcelms.formConfig.service.FormConfigService;import io.swagger.annotations.Api;import io.swagger.annotations.ApiOperation;import org.springframework.beans.factory.annotation.Autowired;import org.springframework.web.bind.annotation.PostMapping;import org.springframework.web.bind.annotation.RequestBody;import org.springframework.web.bind.annotation.RequestMapping;import org.springframework.web.bind.annotation.RestController;import java.util.List;
@RequestMapping("/api/opportunity")
@Api(tags = "机会API")
@RestController
public class OpportunityController {
    @Autowired
    private FormConfigService formConfigService;

    @ApiOperation(value = "查询详情页配置", notes = "查询详情页配置")
    @PostMapping("/config")
    @LogAnnotation(module = "企业客户API", recordRequestParam = true, action = "查询", desc = "查询详情页配置")
    public Result<OpportunityConfigResVo> getOpportunityConfigs() {
        OpportunityConfigResVo configs = formConfigService.getOpportunityConfigs();
        return Result.succeed(configs, ResultStatusEnum.SUCCESS.getMsg());
    }


    @ApiOperation(value = "查询展示字段", notes = "查询展示字段")
    @PostMapping("/insureFields")
    @LogAnnotation(module = "企业客户API", recordRequestParam = true, action = "查询", desc = "查询展示字段")
    public Result<List<FormConfigField>> getInsureFields(@RequestBody InsureFieldReqVo reqVo) {
        List<FormConfigField> fields = formConfigService.getInsureFields(reqVo.getConfigIds());
        return Result.succeed(fields, ResultStatusEnum.SUCCESS.getMsg());
    }
}
