package com.kbao.kbcelms.questionnaire.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireQueryBean;
import com.kbao.kbcelms.questionnaire.entity.Questionnaire;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷Mapper接口
 */
public interface QuestionnaireMapper extends BaseMapper<Questionnaire, Long> {

    /**
     * 分页查询问卷列表
     */
    List<QuestionnaireVO> selectQuestionnairePage(@Param("query") QuestionnaireQueryBean query);
    /**
     * 根据ID查询问卷详情（包含问题和选项）
     */
    QuestionnaireVO selectQuestionnaire(@Param("id") Long id, @Param("enterpriseType") String enterpriseType);

    /**
     * 查询问卷列表（不分页）
     */
    List<QuestionnaireVO> selectQuestionnaireList(@Param("query") QuestionnaireQueryBean query);



    /**
     * 统计问卷数量
     */
    Long countQuestionnaires(@Param("query") QuestionnaireQueryBean query);
}
