package com.kbao.kbcelms.questionnaire.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.questionnaire.bean.*;
import com.kbao.kbcelms.questionnaire.dao.QuestionnaireMapper;
import com.kbao.kbcelms.questionnaire.entity.Questionnaire;
import com.kbao.kbcelms.questionnaire.enums.QuestionnaireStatusEnum;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireQuestionVO;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireVO;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 问卷服务实现类
 */
@Slf4j
@Service
public class QuestionnaireService extends BaseSQLServiceImpl<Questionnaire, Long, QuestionnaireMapper> {

    @Autowired
    private QuestionnaireQuestionService questionnaireQuestionService;

    @Autowired
    private QuestionnaireQuestionOptionService questionnaireQuestionOptionService;

    public PageInfo<QuestionnaireVO> getQuestionnairePage(PageRequest<QuestionnaireQueryBean> request) {
        try {
            // 构建分页参数
            QuestionnaireQueryBean query = request.getParam();

            PageHelper.startPage(request.getPageNum(), request.getPageSize());

            // 查询数据
            List<QuestionnaireVO> list = this.mapper.selectQuestionnairePage(query);

            // 确保每个问卷的enterpriseTypeList字段被正确设置
            list.forEach(questionnaire -> {
                if (questionnaire.getEnterpriseTypes() != null && !questionnaire.getEnterpriseTypes().trim().isEmpty()) {
                    questionnaire.setEnterpriseTypeList(questionnaire.getEnterpriseTypeList());
                }
            });

            return new PageInfo<>(list);
        } catch (Exception e) {
            log.error("分页查询问卷列表失败", e);
            throw new RuntimeException("查询失败");
        }
    }

    public QuestionnaireVO getQuestionnaire(Long id, String enterpriseType) {
        try {

            QuestionnaireVO questionnaire = this.mapper.selectQuestionnaire(id, enterpriseType);
            if (questionnaire == null) {
                throw new RuntimeException("问卷不存在");
            }

            // 确保enterpriseTypeList字段被正确设置
            if (questionnaire.getEnterpriseTypes() != null && !questionnaire.getEnterpriseTypes().trim().isEmpty()) {
                // 将逗号分隔的字符串转换为列表
                String[] types = questionnaire.getEnterpriseTypes().split(",");
                List<String> typeList = new ArrayList<>();
                for (String type : types) {
                    if (type != null && !type.trim().isEmpty()) {
                        typeList.add(type.trim());
                    }
                }
                questionnaire.setEnterpriseTypeList(typeList);
            } else {
                questionnaire.setEnterpriseTypeList(new ArrayList<>());
            }

            return questionnaire;
        } catch (Exception e) {
            log.error("查询问卷详情失败，id: {}", id, e);
            throw new RuntimeException("查询失败");
        }
    }

    /**
     * 获取问卷完整详情（包含题目和选项的完整信息）
     */
    public QuestionnaireVO getQuestionnaireDetail(Long id, String enterpriseType) {
        try {
            // 1. 获取问卷基本信息
            QuestionnaireVO questionnaire = getQuestionnaire(id, enterpriseType);

            // 2. 获取完整的题目和选项信息
            List<QuestionnaireQuestionVO> questions = questionnaireQuestionService.getQuestionsByQuestionnaireId(questionnaire.getId());
            questionnaire.setQuestions(questions);

            return questionnaire;
        } catch (Exception e) {
            log.error("查询问卷完整详情失败，id: {}", id, e);
            throw new RuntimeException("查询失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Long saveQuestionnaire(QuestionnaireSaveBean saveBean) {
        try {
            // 验证参数
            validateSaveBean(saveBean);

            Questionnaire questionnaire = new Questionnaire();
            BeanUtils.copyProperties(saveBean, questionnaire);

            // 处理企业类型
            if (!CollectionUtils.isEmpty(saveBean.getEnterpriseTypes())) {
                questionnaire.setEnterpriseTypes(String.join(",", saveBean.getEnterpriseTypes()));
            }

            LocalDateTime now = LocalDateTime.now();
            String currentUser = getCurrentUser();

            // 判断是否包含题目数据
            boolean hasQuestions = !CollectionUtils.isEmpty(saveBean.getQuestions());
            log.info("保存问卷，ID: {}, 是否包含题目: {}", saveBean.getId(), hasQuestions);

            if (saveBean.getId() == null) {
                // 新增
                questionnaire.setCreateTime(now);
                questionnaire.setUpdateTime(now);
                questionnaire.setCreateUser(currentUser);
                questionnaire.setUpdateUser(currentUser);
                questionnaire.setDeleted(0);

                this.mapper.insert(questionnaire);
                log.info("新增问卷成功，ID: {}", questionnaire.getId());
            } else {
                // 编辑
                questionnaire.setUpdateTime(now);
                questionnaire.setUpdateUser(currentUser);

                this.updateByPrimaryKeySelective(questionnaire);
                log.info("更新问卷基本信息成功，ID: {}", questionnaire.getId());

                // 只有在包含题目数据时才删除原有问题和选项
                if (hasQuestions) {
                    log.info("检测到题目数据，删除原有题目，问卷ID: {}", questionnaire.getId());
                    questionnaireQuestionService.deleteByQuestionnaireId(questionnaire.getId());
                }
            }

            // 只有在包含题目数据时才保存问题和选项
            if (hasQuestions) {
                log.info("保存题目数据，问卷ID: {}, 题目数量: {}", questionnaire.getId(), saveBean.getQuestions().size());
                questionnaireQuestionService.saveQuestionsAndOptions(questionnaire.getId(), saveBean.getQuestions());
            } else {
                log.info("未包含题目数据，跳过题目保存，问卷ID: {}", questionnaire.getId());
            }

            return questionnaire.getId();
        } catch (Exception e) {
            log.error("保存问卷失败", e);
            throw new RuntimeException("保存失败");
        }
    }

    /**
     * 保存问卷基本信息（不包含题目）
     * 专门用于前端基本信息编辑页面
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveQuestionnaireBasicInfo(QuestionnaireSaveBean saveBean) {
        try {
            log.info("保存问卷基本信息，ID: {}", saveBean.getId());

            // 验证参数
            validateSaveBean(saveBean);

            Questionnaire questionnaire = new Questionnaire();
            BeanUtils.copyProperties(saveBean, questionnaire);

            // 处理企业类型
            if (!CollectionUtils.isEmpty(saveBean.getEnterpriseTypes())) {
                questionnaire.setEnterpriseTypes(String.join(",", saveBean.getEnterpriseTypes()));
            }

            LocalDateTime now = LocalDateTime.now();
            String currentUser = getCurrentUser();

            if (saveBean.getId() == null) {
                // 新增
                questionnaire.setCreateTime(now);
                questionnaire.setUpdateTime(now);
                questionnaire.setCreateUser(currentUser);
                questionnaire.setUpdateUser(currentUser);
                questionnaire.setDeleted(0);

                this.mapper.insert(questionnaire);
                log.info("新增问卷基本信息成功，ID: {}", questionnaire.getId());
            } else {
                // 编辑 - 只更新基本信息，不影响题目
                questionnaire.setUpdateTime(now);
                questionnaire.setUpdateUser(currentUser);

                this.updateByPrimaryKeySelective(questionnaire);
                log.info("更新问卷基本信息成功，ID: {}", questionnaire.getId());
            }

            return questionnaire.getId();
        } catch (Exception e) {
            log.error("保存问卷基本信息失败", e);
            throw new RuntimeException("保存基本信息失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQuestionnaire(Long id) {
        try {
            if (id == null) {
                throw new IllegalArgumentException("问卷ID不能为空");
            }

            // 逻辑删除问卷
            Questionnaire questionnaire = new Questionnaire();
            questionnaire.setId(id);
            questionnaire.setDeleted(1);
            questionnaire.setUpdateTime(LocalDateTime.now());
            questionnaire.setUpdateUser(getCurrentUser());
            
            int result = this.mapper.updateByPrimaryKeySelective(questionnaire);
            
            if (result > 0) {
                // 删除相关问题和选项
                questionnaireQuestionService.deleteByQuestionnaireId(id);
            }
            
            return result > 0;
        } catch (Exception e) {
            log.error("删除问卷失败，id: {}", id, e);
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 验证保存参数
     */
    private void validateSaveBean(QuestionnaireSaveBean saveBean) {
        if (saveBean == null) {
            throw new IllegalArgumentException("保存参数不能为空");
        }
        if (!StringUtils.hasText(saveBean.getTitle())) {
            throw new IllegalArgumentException("问卷标题不能为空");
        }
        if (saveBean.getStatus() == null || !QuestionnaireStatusEnum.isValidValue(saveBean.getStatus())) {
            throw new IllegalArgumentException("状态值无效");
        }
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        return SysLoginUtils.getUserId();
    }
}
