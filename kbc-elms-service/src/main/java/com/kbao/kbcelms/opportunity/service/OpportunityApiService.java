package com.kbao.kbcelms.opportunity.service;

import com.github.pagehelper.PageHelper;import com.github.pagehelper.PageInfo;import com.kbao.commons.snowflake.IdWorker;import com.kbao.commons.web.PageRequest;import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enums.OpportunityStatusEnum;import com.kbao.kbcelms.opportunity.dao.OpportunityMapper;import com.kbao.kbcelms.opportunity.entity.Opportunity;import com.kbao.kbcelms.opportunity.vo.OpportunityAddReqVo;import com.kbao.kbcelms.opportunity.vo.OpportunityListResVo;import com.kbao.kbcelms.opportunity.vo.OpportunitySearchReqVo;import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;import com.kbao.kbcelms.opportunitydetail.vo.OpportunityDetailVO;import com.kbao.kbcucs.context.RequestContext;import com.kbao.kbcucs.user.model.RequestInfo;import com.kbao.kbcucs.user.model.UserInfoResp;import com.kbao.tool.util.IDUtils;import org.springframework.beans.factory.annotation.Autowired;import org.springframework.stereotype.Service;import org.springframework.transaction.annotation.Transactional;import java.util.Date;import java.util.List;
@Service
public class OpportunityApiService {
    @Autowired
    private OpportunityMapper opportunityMapper;
    @Autowired
    private OpportunityDetailService opportunityDetailService;

    @Transactional(rollbackFor = Exception.class)
    public void add(OpportunityAddReqVo reqVo) {
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();
        Opportunity oppo = new Opportunity();
        oppo.setOpportunityName(reqVo.getOpportunityName());
        oppo.setAgentEnterpriseId(reqVo.getAgentEnterpriseId());
        oppo.setOpportunityType(reqVo.getOpportunityType());
        oppo.setStatus(reqVo.getStatus());
        // 补充创建人信息
        oppo.setBizCode(IDUtils.generateIds("P"));
        oppo.setAgentCode(userInfo.getAgentCode());
        oppo.setAgentName(userInfo.getAgentName());
        oppo.setAreaCenterCode(userInfo.getAreaCenterCode());
        oppo.setAreaCenterName(userInfo.getAreaCenterName());
        oppo.setLegalCode(userInfo.getLegalCode());
        oppo.setLegalName(userInfo.getLegalName());
        oppo.setCompanyCode(userInfo.getCompanyCode());
        oppo.setCompanyName(userInfo.getCompanyName());
        oppo.setTradingCenterCode(userInfo.getTradingCenterCode());
        oppo.setTradingCenterName(userInfo.getTradingCenterName());
        oppo.setSalesCenterCode(userInfo.getSalesCenterCode());
        oppo.setSalesCenterName(userInfo.getSalesCenterName());
        oppo.setCreateId(userInfo.getId());
        oppo.setTenantId(userInfo.getTenantId());
        opportunityMapper.insert(oppo);

        OpportunityDetail detail = oppo.getDetail();
        detail.setOpportunityId(oppo.getId());
        if (OpportunityStatusEnum.SUBMITTED.getCode().equals(oppo.getStatus())) {
            detail.setSubmitTime(new Date());
        }
        opportunityDetailService.saveOpportunityDetail(detail);
    }

    public PageInfo<OpportunityListResVo> getAgentOpportunityList(PageRequest<OpportunitySearchReqVo> pageRequest) {
        // todo 补充锁定逻辑
        OpportunitySearchReqVo param = pageRequest.getParam();
        param.setAgentCode(ElmsContext.getUser().getAgentCode());
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<OpportunityListResVo> list = opportunityMapper.getAgentOpportunityList(param);
        return new PageInfo<>(list);
    }
}
