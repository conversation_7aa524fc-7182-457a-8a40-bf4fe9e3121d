package com.kbao.kbcelms.opportunity.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.export.ExcelUtils;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.formConfig.model.FormConfigField;
import com.kbao.kbcelms.formConfig.dao.FormConfigFieldDao;
import com.kbao.kbcelms.opportunity.dao.OpportunityInsureInfoDao;import com.kbao.kbcelms.opportunity.model.OpportunityInsureInfo;import com.kbao.tool.util.EmptyUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 表单配置字段服务类
 * @Date 2025-08-15
 */
@Service
public class OpportunityInsureInfoService extends BaseMongoServiceImpl<OpportunityInsureInfo, String, OpportunityInsureInfoDao> {

}
