package com.kbao.kbcelms.genAgentEnterprise.service;

import com.alibaba.nacos.common.utils.CollectionUtils;import com.github.pagehelper.PageHelper;import com.github.pagehelper.PageInfo;import com.kbao.commons.web.PageRequest;import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseQueryRecord;import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseQueryRecordService;import com.kbao.kbcelms.enterprise.type.bean.EnterpriseTypeEnumVO;import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListResVo;import com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper;import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;import com.kbao.kbcelms.util.StringUtils;import com.kbao.kbcucs.context.RequestContext;import com.kbao.kbcucs.user.model.UserInfoResp;import com.kbao.tool.util.SysLoginUtils;import jodd.util.CollectionUtil;import org.joda.time.DateTime;import org.joda.time.LocalDate;import org.springframework.beans.factory.annotation.Autowired;import org.springframework.stereotype.Service;import java.util.*;import java.util.stream.Collectors;
@Service
public class AgentEnterpriseApiService {
    @Autowired
    private GenAgentEnterpriseMapper genAgentEnterpriseMapper;
    @Autowired
    private EnterpriseTypeService enterpriseTypeService;
    @Autowired
    private EnterpriseQueryRecordService enterpriseQueryRecordService;

    public PageInfo<GenAgentEnterprise> getAgentEnterpriseList(PageRequest<AgentEnterpriseListReqVo> pageRequest) {
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();
        Map<String,String> typeMap = enterpriseTypeService.getEnterpriseTypeMap();

        AgentEnterpriseListReqVo param = pageRequest.getParam();
        param.setAgentCode(userInfo.getAgentCode());
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<GenAgentEnterprise> enterpriseList = genAgentEnterpriseMapper.getEnterpriseListByAgentCode(param);
        if (CollectionUtils.isNotEmpty(enterpriseList)) {
            enterpriseList.forEach(enterprise -> {
                enterprise.setDtType(typeMap.get(enterprise.getDtType()));
                Date verifyTime = enterprise.getVerifyTime();
                if (verifyTime == null || LocalDate.fromDateFields(verifyTime).plusYears(1).isAfter(LocalDate.now())){
                    enterprise.setIsVerified("0");
                }
            });
        }
        return new PageInfo<>(enterpriseList);
    }

    public GenAgentEnterprise getById(Integer id) {
        GenAgentEnterprise enterprise = genAgentEnterpriseMapper.selectByPrimaryKey(id);
        Date verifyTime = enterprise.getVerifyTime();
        if (verifyTime == null || LocalDate.fromDateFields(verifyTime).plusYears(1).isAfter(LocalDate.now())){
            enterprise.setIsVerified("0");
        }
        return enterprise;
    }

    public void saveAgentEnterprise(GenAgentEnterprise enterprise) {
        // todo 添加行业校验逻辑

        UserInfoResp userInfo = RequestContext.UserInfoResp.get();
        String dtType = StringUtils.getMaxStr(enterprise.getStaffScale(), enterprise.getAnnualIncome());
        enterprise.setDtType(dtType);
        enterprise.setAgentCode(userInfo.getAgentCode());
        enterprise.setTenantId(RequestContext.TenantId.get());
        String queryRecordId = enterprise.getQueryRecordId();
        EnterpriseQueryRecord queryRecord = enterpriseQueryRecordService.findById(queryRecordId);
        enterprise.setIsVerified("0");
        if (queryRecord != null) {
            enterprise.setQueryRecordId(queryRecord.getId());
            enterprise.setIsVerified("1");
            enterprise.setVerifyTime(queryRecord.getQueryTime());
        }
        if (enterprise.getId() == null) {
            enterprise.setCreateId(userInfo.getId());
            genAgentEnterpriseMapper.insert(enterprise);
        } else {
            enterprise.setUpdateId(userInfo.getId());
            genAgentEnterpriseMapper.updateByPrimaryKey(enterprise);
        }
    }
}
