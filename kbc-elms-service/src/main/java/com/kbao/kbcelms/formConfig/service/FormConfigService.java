package com.kbao.kbcelms.formConfig.service;

import cn.hutool.core.collection.CollectionUtil;import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.common.constants.ElmsConstant;import com.kbao.kbcelms.common.enums.FormConfigTypeEnum;
import com.kbao.kbcelms.formConfig.bean.OpportunityConfigResVo;
import com.kbao.kbcelms.formConfig.entity.FormConfig;
import com.kbao.kbcelms.formConfig.dao.FormConfigMapper;
import com.kbao.kbcelms.formConfig.model.FormConfigField;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.function.Function;import java.util.stream.Collectors;import static com.kbao.kbcelms.common.enums.FormConfigTypeEnum.EMPLOYEE_WELFARE;

/**
 * <AUTHOR>
 * @Description 表单配置服务类
 * @Date 2025-08-15
 */
@Service
public class FormConfigService extends BaseSQLServiceImpl<FormConfig, Integer, FormConfigMapper> {
    
    @Autowired
    private FormConfigFieldService formConfigFieldService;
    
    /**
     * 新增表单配置及其字段
     * @param formConfig 表单配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void addWithFields(FormConfig formConfig) {
        // 校验configCode唯一性
        if (formConfig.getConfigCode() == null) {
            throw new RuntimeException("配置编码不能为空");
        }
        int count = mapper.isExistConfigCode(formConfig.getConfigCode(), null);
        if (count > 0) {
            throw new RuntimeException("配置编码已存在");
        }
        // 设置基础信息
        formConfig.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        formConfig.setCreateId(SysLoginUtils.getUserId());
        formConfig.setCreateTime(new Date());
        formConfig.setUpdateTime(new Date());
        formConfig.setIsDeleted(0);

        // 设置默认状态为启用
        if (formConfig.getStatus() == null) {
            formConfig.setStatus("1");
        }
        
        // 保存主表
        this.insert(formConfig);
        
        // 保存字段
        if (formConfig.getFields() != null && !formConfig.getFields().isEmpty()) {
            formConfigFieldService.saveBatch(formConfig.getId(), formConfig.getFields());
        }
    }
    
    /**
     * 更新表单配置及其字段
     * @param formConfig 表单配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateWithFields(FormConfig formConfig) {
        if (formConfig.getConfigCode() == null) {
            throw new RuntimeException("配置编码不能为空");
        }
        int count = mapper.isExistConfigCode(formConfig.getConfigCode(), formConfig.getId());
        if (count > 0) {
            throw new RuntimeException("配置编码已存在");
        }
        
        // 设置更新信息
        formConfig.setUpdateId(SysLoginUtils.getUserId());
        formConfig.setUpdateTime(new Date());
        
        // 更新主表
        this.updateByPrimaryKeySelective(formConfig);
        
        // 更新字段
        formConfigFieldService.saveBatch(formConfig.getId(), formConfig.getFields());
    }
    
    /**
     * 根据ID获取表单配置（包含字段）
     * @param id 配置ID
     * @return 表单配置
     */
    public FormConfig getById(Integer id) {
        FormConfig formConfig = mapper.selectByPrimaryKey(id);
        if (formConfig != null) {
            List<FormConfigField> fieldList = formConfigFieldService.getFieldList(formConfig.getId());
            formConfig.setFields(fieldList);
        }
        return formConfig;
    }
    
    /**
     * 根据配置编码获取字段列表
     * @param configCode 配置编码
     * @return 字段列表
     */
    public List<FormConfigField> getFieldList(String configCode) {
        FormConfig formConfig = mapper.selectByConfigCode(configCode);
        if (formConfig != null) {
            return formConfigFieldService.getFieldList(formConfig.getId());
        }
        return null;
    }
    

    public OpportunityConfigResVo getOpportunityConfigs() {
        OpportunityConfigResVo result = new OpportunityConfigResVo();
        List<FormConfig> configs = mapper.selectByType(EMPLOYEE_WELFARE.getCode());
        if (configs == null) {
            return result;
        }
        result.setInsureTypes(configs);
        List<FormConfigField> fieldList = this.getFieldList(ElmsConstant.FORM_CONFIG_AGENT_ENTERPRISE_SUPP);
        result.setFields(fieldList);
        return result;
    }

    public List<FormConfigField> getInsureFields(List<Integer> configIds) {
        List<FormConfigField> configFields = formConfigFieldService.getByConfigIds(configIds);
        if (CollectionUtil.isNotEmpty(configFields)) {
            Set<String> fieldCodes = new HashSet<>();
            configFields = configFields.stream().filter(field -> fieldCodes.add(field.getFieldCode()))
                .collect(Collectors.toList());
        }
        return configFields;
    }
}
