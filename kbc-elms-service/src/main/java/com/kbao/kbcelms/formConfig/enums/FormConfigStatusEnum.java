package com.kbao.kbcelms.formConfig.enums;

/**
 * <AUTHOR>
 * @Description 表单配置状态枚举
 * @Date 2025-08-15
 */
public enum FormConfigStatusEnum {
    
    /**
     * 禁用
     */
    DISABLED("0", "禁用"),
    
    /**
     * 启用
     */
    ENABLED("1", "启用");
    
    private final String code;
    private final String name;
    
    FormConfigStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 枚举
     */
    public static FormConfigStatusEnum getByCode(String code) {
        for (FormConfigStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
    
    /**
     * 根据编码获取名称
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(String code) {
        FormConfigStatusEnum statusEnum = getByCode(code);
        return statusEnum != null ? statusEnum.getName() : "未知";
    }
    
    /**
     * 验证编码是否有效
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
    
    /**
     * 判断是否启用
     * @param code 编码
     * @return 是否启用
     */
    public static boolean isEnabled(String code) {
        return ENABLED.getCode().equals(code);
    }
    
    /**
     * 判断是否禁用
     * @param code 编码
     * @return 是否禁用
     */
    public static boolean isDisabled(String code) {
        return DISABLED.getCode().equals(code);
    }
}
