package com.kbao.kbcelms.formConfig.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.formConfig.model.FormConfigField;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 表单配置字段Dao类
 * @Date 2025-08-15
 */
@Repository
public class FormConfigFieldDao extends BaseMongoDaoImpl<FormConfigField, String> {
    
    /**
     * 根据配置ID删除字段
     * @param configId 配置ID
     */
    public void deleteByConfigId(Integer configId) {
        Query query = new Query(Criteria.where("configId").is(configId));
        mongoTemplate.remove(query, FormConfigField.class);
    }
    
    /**
     * 根据配置ID查询字段列表，按排序字段排序
     * @param configId 配置ID
     * @return 字段列表
     */
    public List<FormConfigField> findByConfigIdOrderBySort(Integer configId) {
        Query query = new Query(Criteria.where("configId").is(configId));
        query.with(Sort.by(Sort.Direction.ASC, "sort"));
        return mongoTemplate.find(query, FormConfigField.class);
    }

    public List<FormConfigField> getByConfigIds(List<Integer> configIds) {
            Query query = new Query(Criteria.where("configId").in(configIds));
            query.with(Sort.by(Sort.Direction.ASC, "sort"));
            return mongoTemplate.find(query, FormConfigField.class);
        }
    
    /**
     * 根据配置ID统计字段数量
     * @param configId 配置ID
     * @return 字段数量
     */
    public long countByConfigId(Integer configId) {
        Query query = new Query(Criteria.where("configId").is(configId));
        return mongoTemplate.count(query, FormConfigField.class);
    }
}
